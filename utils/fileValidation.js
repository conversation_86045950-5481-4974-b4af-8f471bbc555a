const fs = require('fs-extra');
const path = require('path');
// Dynamic import for file-type ES module will be used in methods

/**
 * Enhanced file upload security utilities
 */
class FileValidationUtils {
    constructor() {
        // Allowed MIME types for certificate files
        this.allowedMimeTypes = [
            'application/x-pem-file',
            'application/x-x509-ca-cert',
            'text/plain',
            'application/octet-stream'
        ];

        // Maximum file size (5MB)
        this.maxFileSize = 5 * 1024 * 1024;

        // Allowed file extensions
        this.allowedExtensions = ['.pem', '.crt', '.cert', '.key', '.pub'];

        // Suspicious patterns that might indicate malicious content
        this.suspiciousPatterns = [
            /<script/i,
            /javascript:/i,
            /vbscript:/i,
            /onload=/i,
            /onerror=/i,
            /eval\(/i,
            /document\./i,
            /window\./i,
            /<iframe/i,
            /<object/i,
            /<embed/i,
            /data:text\/html/i,
            /\x00/g, // null bytes
            /\x01/g, // control characters
            /\x02/g,
            /\x03/g,
            /\x04/g,
            /\x05/g,
            /\x06/g,
            /\x07/g,
            /\x08/g,
            /\x0B/g,
            /\x0C/g,
            /\x0E/g,
            /\x0F/g
        ];

        // PEM certificate patterns
        this.pemPatterns = {
            certificate: /-----BEGIN CERTIFICATE-----[\s\S]*?-----END CERTIFICATE-----/g,
            publicKey: /-----BEGIN PUBLIC KEY-----[\s\S]*?-----END PUBLIC KEY-----/g,
            rsaPublicKey: /-----BEGIN RSA PUBLIC KEY-----[\s\S]*?-----END RSA PUBLIC KEY-----/g,
            privateKey: /-----BEGIN PRIVATE KEY-----[\s\S]*?-----END PRIVATE KEY-----/g,
            rsaPrivateKey: /-----BEGIN RSA PRIVATE KEY-----[\s\S]*?-----END RSA PRIVATE KEY-----/g
        };
    }

    /**
     * Validate uploaded file for security
     * @param {Object} file - Multer file object
     * @returns {Promise<Object>} - Validation result
     */
    async validateUploadedFile(file) {
        try {
            if (!file) {
                return {
                    isValid: false,
                    error: 'No file provided',
                    details: null
                };
            }

            // Check file size
            if (file.size > this.maxFileSize) {
                return {
                    isValid: false,
                    error: `File size exceeds maximum allowed size of ${this.maxFileSize / (1024 * 1024)}MB`,
                    details: { size: file.size, maxSize: this.maxFileSize }
                };
            }

            // Check file extension
            const fileExtension = path.extname(file.originalname).toLowerCase();
            if (!this.allowedExtensions.includes(fileExtension)) {
                return {
                    isValid: false,
                    error: `File extension '${fileExtension}' is not allowed. Allowed extensions: ${this.allowedExtensions.join(', ')}`,
                    details: { extension: fileExtension, allowedExtensions: this.allowedExtensions }
                };
            }

            // Read file content for analysis
            const fileContent = await fs.readFile(file.path);
            const contentString = fileContent.toString('utf8');

            // Detect file type from content using dynamic import
            let detectedType = null;
            try {
                const { fileTypeFromBuffer } = await import('file-type');
                detectedType = await fileTypeFromBuffer(fileContent);
            } catch (error) {
                // If file-type fails, continue with content-based validation
                console.warn('File type detection failed:', error.message);
            }
            
            // Validate MIME type (allow text files and unknown types for PEM files)
            if (detectedType && !this.allowedMimeTypes.includes(detectedType.mime) && 
                !detectedType.mime.startsWith('text/')) {
                return {
                    isValid: false,
                    error: `File type '${detectedType.mime}' is not allowed`,
                    details: { detectedMime: detectedType.mime, allowedMimes: this.allowedMimeTypes }
                };
            }

            // Check for suspicious content patterns
            const suspiciousCheck = this.checkSuspiciousContent(contentString);
            if (!suspiciousCheck.isValid) {
                return suspiciousCheck;
            }

            // Validate PEM certificate format
            const pemValidation = this.validatePemFormat(contentString);
            if (!pemValidation.isValid) {
                return pemValidation;
            }

            // Simulate virus scanning (placeholder for real antivirus integration)
            const virusCheck = this.simulateVirusScan(fileContent);
            if (!virusCheck.isValid) {
                return virusCheck;
            }

            return {
                isValid: true,
                error: null,
                details: {
                    size: file.size,
                    extension: fileExtension,
                    detectedMime: detectedType?.mime || 'text/plain',
                    pemType: pemValidation.pemType,
                    contentLength: contentString.length
                }
            };

        } catch (error) {
            return {
                isValid: false,
                error: `File validation failed: ${error.message}`,
                details: null
            };
        }
    }

    /**
     * Check for suspicious content patterns
     * @param {string} content - File content as string
     * @returns {Object} - Validation result
     */
    checkSuspiciousContent(content) {
        for (const pattern of this.suspiciousPatterns) {
            if (pattern.test(content)) {
                return {
                    isValid: false,
                    error: 'File contains suspicious content that may be malicious',
                    details: { suspiciousPattern: pattern.toString() }
                };
            }
        }

        // Check for excessive repetition (potential zip bomb or similar)
        const lines = content.split('\n');
        if (lines.length > 10000) {
            return {
                isValid: false,
                error: 'File contains too many lines (potential security risk)',
                details: { lineCount: lines.length, maxLines: 10000 }
            };
        }

        // Check for extremely long lines (potential buffer overflow attempt)
        const maxLineLength = Math.max(...lines.map(line => line.length));
        if (maxLineLength > 10000) {
            return {
                isValid: false,
                error: 'File contains extremely long lines (potential security risk)',
                details: { maxLineLength, maxAllowed: 10000 }
            };
        }

        return {
            isValid: true,
            error: null,
            details: null
        };
    }

    /**
     * Validate PEM certificate format
     * @param {string} content - File content as string
     * @returns {Object} - Validation result
     */
    validatePemFormat(content) {
        let pemType = null;
        let hasValidPem = false;

        // Check for different types of PEM content
        for (const [type, pattern] of Object.entries(this.pemPatterns)) {
            if (pattern.test(content)) {
                pemType = type;
                hasValidPem = true;
                break;
            }
        }

        if (!hasValidPem) {
            return {
                isValid: false,
                error: 'File does not contain valid PEM format certificate or key',
                details: { expectedFormats: Object.keys(this.pemPatterns) }
            };
        }

        // Validate PEM structure
        const pemBlocks = content.match(/-----BEGIN [A-Z\s]+-----[\s\S]*?-----END [A-Z\s]+-----/g);
        if (!pemBlocks || pemBlocks.length === 0) {
            return {
                isValid: false,
                error: 'Invalid PEM block structure',
                details: null
            };
        }

        // Check each PEM block
        for (const block of pemBlocks) {
            const lines = block.split('\n');
            const beginLine = lines[0];
            const endLine = lines[lines.length - 1];

            // Extract the label from BEGIN and END lines
            const beginMatch = beginLine.match(/-----BEGIN ([A-Z\s]+)-----/);
            const endMatch = endLine.match(/-----END ([A-Z\s]+)-----/);

            if (!beginMatch || !endMatch || beginMatch[1] !== endMatch[1]) {
                return {
                    isValid: false,
                    error: 'PEM block has mismatched BEGIN/END labels',
                    details: { beginLabel: beginMatch?.[1], endLabel: endMatch?.[1] }
                };
            }

            // Validate base64 content
            const base64Content = lines.slice(1, -1).join('');
            if (!this.isValidBase64(base64Content)) {
                return {
                    isValid: false,
                    error: 'PEM block contains invalid base64 content',
                    details: null
                };
            }
        }

        return {
            isValid: true,
            error: null,
            pemType: pemType,
            details: { pemBlockCount: pemBlocks.length }
        };
    }

    /**
     * Validate base64 content
     * @param {string} content - Base64 content to validate
     * @returns {boolean} - Whether content is valid base64
     */
    isValidBase64(content) {
        try {
            // Remove whitespace and check if it's valid base64
            const cleaned = content.replace(/\s/g, '');
            if (cleaned.length === 0) return false;
            
            // Check if it's valid base64 pattern
            const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
            if (!base64Pattern.test(cleaned)) return false;
            
            // Try to decode it
            Buffer.from(cleaned, 'base64');
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * Simulate virus scanning (placeholder for real antivirus integration)
     * @param {Buffer} fileContent - File content as buffer
     * @returns {Object} - Scan result
     */
    simulateVirusScan(fileContent) {
        // In a real implementation, this would integrate with antivirus software
        // For now, we'll do basic checks for known malicious patterns
        
        const contentString = fileContent.toString('utf8');
        
        // Check for EICAR test string (standard antivirus test)
        if (contentString.includes('X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*')) {
            return {
                isValid: false,
                error: 'File contains EICAR test virus signature',
                details: { scanResult: 'EICAR_TEST_FILE' }
            };
        }

        // Check for other suspicious binary patterns
        const binaryContent = fileContent.toString('hex');
        const suspiciousBinaryPatterns = [
            '4d5a', // MZ header (Windows executable)
            '7f454c46', // ELF header (Linux executable)
            'cafebabe', // Java class file
            '504b0304', // ZIP file header
            '526172211a07', // RAR file header
        ];

        for (const pattern of suspiciousBinaryPatterns) {
            if (binaryContent.toLowerCase().startsWith(pattern)) {
                return {
                    isValid: false,
                    error: 'File appears to be an executable or archive file, which is not allowed',
                    details: { detectedPattern: pattern }
                };
            }
        }

        return {
            isValid: true,
            error: null,
            details: { scanResult: 'CLEAN' }
        };
    }

    /**
     * Generate secure filename
     * @param {string} originalName - Original filename
     * @returns {string} - Secure filename
     */
    generateSecureFilename(originalName) {
        const extension = path.extname(originalName).toLowerCase();
        const baseName = path.basename(originalName, extension);
        
        // Sanitize base name
        const sanitizedBaseName = baseName
            .replace(/[^a-zA-Z0-9\-_]/g, '_')
            .substring(0, 50); // Limit length
        
        // Generate timestamp
        const timestamp = Date.now();
        
        // Generate random suffix
        const randomSuffix = Math.random().toString(36).substring(2, 8);
        
        return `${sanitizedBaseName}_${timestamp}_${randomSuffix}${extension}`;
    }
}

module.exports = new FileValidationUtils();
