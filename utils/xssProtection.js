const escapeHtml = require('escape-html');

/**
 * Comprehensive XSS protection utilities
 */
class XSSProtection {
    constructor() {
        // Dangerous HTML tags that should be completely removed
        this.dangerousTags = [
            'script', 'iframe', 'object', 'embed', 'applet', 'form', 'input', 
            'button', 'select', 'textarea', 'link', 'meta', 'style', 'base',
            'frame', 'frameset', 'noframes', 'noscript'
        ];

        // Dangerous attributes that should be removed
        this.dangerousAttributes = [
            'onload', 'onerror', 'onclick', 'onmouseover', 'onmouseout', 'onmousedown',
            'onmouseup', 'onkeydown', 'onkeyup', 'onkeypress', 'onfocus', 'onblur',
            'onchange', 'onsubmit', 'onreset', 'onselect', 'onunload', 'onbeforeunload',
            'onresize', 'onscroll', 'ondrag', 'ondrop', 'oncontextmenu', 'oninput',
            'onpaste', 'oncopy', 'oncut', 'onabort', 'oncanplay', 'oncanplaythrough',
            'ondurationchange', 'onemptied', 'onended', 'onerror', 'onloadeddata',
            'onloadedmetadata', 'onloadstart', 'onpause', 'onplay', 'onplaying',
            'onprogress', 'onratechange', 'onseeked', 'onseeking', 'onstalled',
            'onsuspend', 'ontimeupdate', 'onvolumechange', 'onwaiting', 'onwheel',
            'javascript:', 'vbscript:', 'data:', 'mocha:', 'livescript:'
        ];

        // Dangerous protocols
        this.dangerousProtocols = [
            'javascript:', 'vbscript:', 'data:', 'mocha:', 'livescript:', 'file:'
        ];

        // XSS patterns to detect and remove
        this.xssPatterns = [
            /<script[\s\S]*?>[\s\S]*?<\/script>/gi,
            /<iframe[\s\S]*?>[\s\S]*?<\/iframe>/gi,
            /<object[\s\S]*?>[\s\S]*?<\/object>/gi,
            /<embed[\s\S]*?>/gi,
            /<applet[\s\S]*?>[\s\S]*?<\/applet>/gi,
            /<form[\s\S]*?>[\s\S]*?<\/form>/gi,
            /<link[\s\S]*?>/gi,
            /<meta[\s\S]*?>/gi,
            /<style[\s\S]*?>[\s\S]*?<\/style>/gi,
            /<base[\s\S]*?>/gi,
            /javascript:/gi,
            /vbscript:/gi,
            /data:text\/html/gi,
            /on\w+\s*=/gi,
            /expression\s*\(/gi,
            /url\s*\(/gi,
            /&#x/gi,
            /&#\d/gi,
            /&\w+;/gi
        ];
    }

    /**
     * Sanitize text input to prevent XSS attacks
     * @param {string} input - Input text to sanitize
     * @param {Object} options - Sanitization options
     * @returns {string} - Sanitized text
     */
    sanitizeText(input, options = {}) {
        if (!input || typeof input !== 'string') {
            return '';
        }

        let sanitized = input;

        // Remove null bytes and control characters
        sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

        // Remove dangerous patterns
        for (const pattern of this.xssPatterns) {
            sanitized = sanitized.replace(pattern, '');
        }

        // HTML escape the content
        sanitized = escapeHtml(sanitized);

        // Additional cleaning for specific contexts
        if (options.removeNewlines) {
            sanitized = sanitized.replace(/[\r\n]/g, ' ');
        }

        if (options.maxLength) {
            sanitized = sanitized.substring(0, options.maxLength);
        }

        return sanitized.trim();
    }

    /**
     * Sanitize HTML content (more aggressive than text sanitization)
     * @param {string} html - HTML content to sanitize
     * @returns {string} - Sanitized HTML
     */
    sanitizeHtml(html) {
        if (!html || typeof html !== 'string') {
            return '';
        }

        let sanitized = html;

        // Remove dangerous tags completely
        for (const tag of this.dangerousTags) {
            const regex = new RegExp(`<${tag}[^>]*>.*?<\/${tag}>`, 'gis');
            sanitized = sanitized.replace(regex, '');
            
            // Also remove self-closing versions
            const selfClosingRegex = new RegExp(`<${tag}[^>]*\/?>`, 'gi');
            sanitized = sanitized.replace(selfClosingRegex, '');
        }

        // Remove dangerous attributes
        for (const attr of this.dangerousAttributes) {
            const regex = new RegExp(`\\s${attr}\\s*=\\s*[^\\s>]*`, 'gi');
            sanitized = sanitized.replace(regex, '');
        }

        // Remove dangerous protocols
        for (const protocol of this.dangerousProtocols) {
            const regex = new RegExp(protocol, 'gi');
            sanitized = sanitized.replace(regex, '');
        }

        // Final HTML escape
        return escapeHtml(sanitized);
    }

    /**
     * Sanitize URL to prevent XSS through URL parameters
     * @param {string} url - URL to sanitize
     * @returns {string} - Sanitized URL
     */
    sanitizeUrl(url) {
        if (!url || typeof url !== 'string') {
            return '';
        }

        // Remove dangerous protocols
        for (const protocol of this.dangerousProtocols) {
            if (url.toLowerCase().startsWith(protocol)) {
                return '';
            }
        }

        // URL encode dangerous characters
        let sanitized = url.replace(/[<>"']/g, (match) => {
            return encodeURIComponent(match);
        });

        return sanitized;
    }

    /**
     * Sanitize JSON data recursively
     * @param {Object} data - Data object to sanitize
     * @param {Object} options - Sanitization options
     * @returns {Object} - Sanitized data
     */
    sanitizeJsonData(data, options = {}) {
        if (data === null || data === undefined) {
            return data;
        }

        if (typeof data === 'string') {
            return this.sanitizeText(data, options);
        }

        if (typeof data === 'number' || typeof data === 'boolean') {
            return data;
        }

        if (Array.isArray(data)) {
            return data.map(item => this.sanitizeJsonData(item, options));
        }

        if (typeof data === 'object') {
            const sanitized = {};
            for (const [key, value] of Object.entries(data)) {
                // Sanitize both key and value
                const sanitizedKey = this.sanitizeText(key, { maxLength: 100 });
                sanitized[sanitizedKey] = this.sanitizeJsonData(value, options);
            }
            return sanitized;
        }

        return data;
    }

    /**
     * Validate and sanitize cluster name specifically
     * @param {string} name - Cluster name
     * @returns {Object} - Validation result
     */
    sanitizeClusterName(name) {
        if (!name || typeof name !== 'string') {
            return {
                isValid: false,
                error: 'Cluster name is required',
                sanitized: ''
            };
        }

        // Remove any HTML/XSS content
        let sanitized = this.sanitizeText(name, { removeNewlines: true, maxLength: 253 });
        
        // Additional validation for Kubernetes naming
        sanitized = sanitized.toLowerCase().replace(/[^a-z0-9\-\.]/g, '');
        
        if (sanitized.length === 0) {
            return {
                isValid: false,
                error: 'Cluster name contains only invalid characters',
                sanitized: ''
            };
        }

        return {
            isValid: true,
            error: null,
            sanitized: sanitized
        };
    }

    /**
     * Create safe response object with sanitized data
     * @param {Object|Array} data - Response data
     * @param {Object} options - Sanitization options
     * @returns {Object|Array} - Safe response object
     */
    createSafeResponse(data, options = {}) {
        const sanitized = this.sanitizeJsonData(data, options);

        // Preserve array structure
        if (Array.isArray(data)) {
            return sanitized;
        }

        // For objects, add metadata
        return {
            ...sanitized,
            _sanitized: true,
            _timestamp: new Date().toISOString()
        };
    }

    /**
     * Middleware for sanitizing request body
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {Function} next - Next middleware function
     */
    sanitizeRequestBody(req, res, next) {
        if (req.body && typeof req.body === 'object') {
            req.body = this.sanitizeJsonData(req.body, { maxLength: 10000 });
        }
        next();
    }

    /**
     * Middleware for sanitizing query parameters
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {Function} next - Next middleware function
     */
    sanitizeQueryParams(req, res, next) {
        if (req.query && typeof req.query === 'object') {
            req.query = this.sanitizeJsonData(req.query, { maxLength: 1000 });
        }
        next();
    }

    /**
     * Check if content contains potential XSS
     * @param {string} content - Content to check
     * @returns {Object} - Check result
     */
    detectXSS(content) {
        if (!content || typeof content !== 'string') {
            return { hasXSS: false, patterns: [] };
        }

        const detectedPatterns = [];
        
        for (const pattern of this.xssPatterns) {
            if (pattern.test(content)) {
                detectedPatterns.push(pattern.toString());
            }
        }

        return {
            hasXSS: detectedPatterns.length > 0,
            patterns: detectedPatterns
        };
    }
}

module.exports = new XSSProtection();
