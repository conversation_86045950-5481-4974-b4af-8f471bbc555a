const yaml = require('js-yaml');
const Joi = require('joi');
const escapeHtml = require('escape-html');

/**
 * Enhanced YAML validation and sanitization utilities
 */
class ValidationUtils {
    constructor() {
        // Define Kubernetes Secret schema
        this.secretSchema = Joi.object({
            apiVersion: Joi.string().valid('v1').required(),
            kind: Joi.string().valid('Secret').required(),
            metadata: Joi.object({
                name: Joi.string().pattern(/^[a-z0-9]([-a-z0-9]*[a-z0-9])?$/).required(),
                namespace: Joi.string().pattern(/^[a-z0-9]([-a-z0-9]*[a-z0-9])?$/).optional(),
                labels: Joi.object().pattern(
                    /^[a-z0-9A-Z]([a-z0-9A-Z\-_.]*[a-z0-9A-Z])?$/,
                    Joi.string()
                ).optional(),
                annotations: Joi.object().pattern(
                    Joi.string(),
                    Joi.string()
                ).optional()
            }).required(),
            type: Joi.string().valid(
                'Opaque', 
                'kubernetes.io/service-account-token',
                'kubernetes.io/dockercfg',
                'kubernetes.io/dockerconfigjson',
                'kubernetes.io/basic-auth',
                'kubernetes.io/ssh-auth',
                'kubernetes.io/tls',
                'bootstrap.kubernetes.io/token'
            ).optional().default('Opaque'),
            data: Joi.object().pattern(
                Joi.string(),
                Joi.string().base64()
            ).optional(),
            stringData: Joi.object().pattern(
                Joi.string(),
                Joi.string()
            ).optional(),
            immutable: Joi.boolean().optional()
        }).or('data', 'stringData');

        // YAML parsing options with security constraints
        this.yamlOptions = {
            schema: yaml.CORE_SCHEMA, // Restrict to core schema only
            json: false, // Disable JSON compatibility mode
            onWarning: (warning) => {
                console.warn('YAML Warning:', warning);
            }
        };

        // Maximum allowed YAML size (1MB)
        this.maxYamlSize = 1024 * 1024;
        
        // Maximum nesting depth
        this.maxDepth = 10;
        
        // Maximum number of keys
        this.maxKeys = 100;
    }

    /**
     * Validate and sanitize YAML input
     * @param {string} yamlString - Raw YAML string
     * @param {string} expectedKind - Expected Kubernetes kind (optional)
     * @returns {Object} - Validation result
     */
    validateYaml(yamlString, expectedKind = null) {
        try {
            // Basic input validation
            if (!yamlString || typeof yamlString !== 'string') {
                return {
                    isValid: false,
                    error: 'YAML input is required and must be a string',
                    sanitizedYaml: null,
                    parsedObject: null
                };
            }

            // Check size limits
            if (yamlString.length > this.maxYamlSize) {
                return {
                    isValid: false,
                    error: `YAML size exceeds maximum allowed size of ${this.maxYamlSize} bytes`,
                    sanitizedYaml: null,
                    parsedObject: null
                };
            }

            // Sanitize input - remove potentially dangerous content
            const sanitizedYaml = this.sanitizeYamlString(yamlString);

            // Parse YAML with security constraints
            let parsedObject;
            try {
                parsedObject = yaml.load(sanitizedYaml, this.yamlOptions);
            } catch (yamlError) {
                return {
                    isValid: false,
                    error: `Invalid YAML syntax: ${yamlError.message}`,
                    sanitizedYaml: sanitizedYaml,
                    parsedObject: null
                };
            }

            // Validate structure depth and complexity
            const structureValidation = this.validateStructure(parsedObject);
            if (!structureValidation.isValid) {
                return {
                    isValid: false,
                    error: structureValidation.error,
                    sanitizedYaml: sanitizedYaml,
                    parsedObject: null
                };
            }

            // Validate against expected kind if specified
            if (expectedKind && parsedObject.kind !== expectedKind) {
                return {
                    isValid: false,
                    error: `Expected kind '${expectedKind}' but got '${parsedObject.kind}'`,
                    sanitizedYaml: sanitizedYaml,
                    parsedObject: null
                };
            }

            return {
                isValid: true,
                error: null,
                sanitizedYaml: sanitizedYaml,
                parsedObject: parsedObject
            };

        } catch (error) {
            return {
                isValid: false,
                error: `Validation failed: ${error.message}`,
                sanitizedYaml: null,
                parsedObject: null
            };
        }
    }

    /**
     * Validate Kubernetes Secret specifically
     * @param {string} yamlString - Raw YAML string
     * @returns {Object} - Validation result with Joi schema validation
     */
    validateSecret(yamlString) {
        const yamlValidation = this.validateYaml(yamlString, 'Secret');
        
        if (!yamlValidation.isValid) {
            return yamlValidation;
        }

        // Additional Joi schema validation for Kubernetes Secret
        const { error, value } = this.secretSchema.validate(yamlValidation.parsedObject, {
            abortEarly: false,
            stripUnknown: true
        });

        if (error) {
            return {
                isValid: false,
                error: `Secret validation failed: ${error.details.map(d => d.message).join(', ')}`,
                sanitizedYaml: yamlValidation.sanitizedYaml,
                parsedObject: null
            };
        }

        return {
            isValid: true,
            error: null,
            sanitizedYaml: yaml.dump(value, { indent: 2, lineWidth: -1, noRefs: true }),
            parsedObject: value
        };
    }

    /**
     * Sanitize YAML string to remove potentially dangerous content
     * @param {string} yamlString - Raw YAML string
     * @returns {string} - Sanitized YAML string
     */
    sanitizeYamlString(yamlString) {
        // Remove null bytes
        let sanitized = yamlString.replace(/\0/g, '');
        
        // Remove excessive whitespace and normalize line endings
        sanitized = sanitized.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
        
        // Remove potential YAML bombs (excessive repetition)
        sanitized = this.preventYamlBombs(sanitized);
        
        // Trim excessive whitespace
        sanitized = sanitized.trim();
        
        return sanitized;
    }

    /**
     * Prevent YAML bombs by detecting excessive repetition
     * @param {string} yamlString - YAML string to check
     * @returns {string} - Safe YAML string
     */
    preventYamlBombs(yamlString) {
        // Check for excessive anchor/alias usage
        const anchorMatches = yamlString.match(/&\w+/g) || [];
        const aliasMatches = yamlString.match(/\*\w+/g) || [];
        
        if (anchorMatches.length > 10 || aliasMatches.length > 50) {
            throw new Error('Potential YAML bomb detected: excessive anchor/alias usage');
        }
        
        // Check for deeply nested structures in the string
        const maxIndentation = Math.max(...yamlString.split('\n').map(line => {
            const match = line.match(/^(\s*)/);
            return match ? match[1].length : 0;
        }));
        
        if (maxIndentation > this.maxDepth * 2) { // Assuming 2 spaces per level
            throw new Error('Potential YAML bomb detected: excessive nesting');
        }
        
        return yamlString;
    }

    /**
     * Validate object structure for depth and complexity
     * @param {Object} obj - Parsed object to validate
     * @returns {Object} - Validation result
     */
    validateStructure(obj, depth = 0) {
        if (depth > this.maxDepth) {
            return {
                isValid: false,
                error: `Object nesting exceeds maximum depth of ${this.maxDepth}`
            };
        }

        if (obj && typeof obj === 'object') {
            const keys = Object.keys(obj);
            
            if (keys.length > this.maxKeys) {
                return {
                    isValid: false,
                    error: `Object has too many keys (${keys.length}), maximum allowed is ${this.maxKeys}`
                };
            }

            for (const key of keys) {
                if (Array.isArray(obj[key])) {
                    if (obj[key].length > this.maxKeys) {
                        return {
                            isValid: false,
                            error: `Array '${key}' has too many elements (${obj[key].length}), maximum allowed is ${this.maxKeys}`
                        };
                    }
                    
                    for (const item of obj[key]) {
                        const result = this.validateStructure(item, depth + 1);
                        if (!result.isValid) return result;
                    }
                } else if (obj[key] && typeof obj[key] === 'object') {
                    const result = this.validateStructure(obj[key], depth + 1);
                    if (!result.isValid) return result;
                }
            }
        }

        return { isValid: true, error: null };
    }

    /**
     * Sanitize text input for safe HTML output
     * @param {string} text - Input text
     * @returns {string} - Escaped HTML
     */
    sanitizeHtml(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }
        return escapeHtml(text);
    }

    /**
     * Validate and sanitize cluster name
     * @param {string} name - Cluster name
     * @returns {Object} - Validation result
     */
    validateClusterName(name) {
        if (!name || typeof name !== 'string') {
            return {
                isValid: false,
                error: 'Cluster name is required'
            };
        }

        const trimmedName = name.trim();
        
        if (trimmedName.length < 1 || trimmedName.length > 253) {
            return {
                isValid: false,
                error: 'Cluster name must be between 1 and 253 characters'
            };
        }

        // Kubernetes DNS-1123 subdomain validation
        const dnsPattern = /^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$/;
        if (!dnsPattern.test(trimmedName)) {
            return {
                isValid: false,
                error: 'Cluster name must be a valid DNS subdomain (lowercase letters, numbers, hyphens, and dots only)'
            };
        }

        return {
            isValid: true,
            error: null,
            sanitizedName: trimmedName
        };
    }

    /**
     * Validate PEM certificate content
     * @param {string} certContent - Certificate content
     * @returns {Object} - Validation result
     */
    validatePemCertificate(certContent) {
        if (!certContent || typeof certContent !== 'string') {
            return {
                isValid: false,
                error: 'Certificate content is required'
            };
        }

        const trimmedContent = certContent.trim();
        
        // Basic PEM format validation
        const pemPattern = /^-----BEGIN [A-Z\s]+-----[\s\S]*-----END [A-Z\s]+-----$/;
        if (!pemPattern.test(trimmedContent)) {
            return {
                isValid: false,
                error: 'Invalid PEM certificate format'
            };
        }

        // Check for reasonable size (max 10KB for a certificate)
        if (trimmedContent.length > 10240) {
            return {
                isValid: false,
                error: 'Certificate file is too large (max 10KB allowed)'
            };
        }

        // Ensure it contains certificate-like content
        if (!trimmedContent.includes('-----BEGIN CERTIFICATE-----') && 
            !trimmedContent.includes('-----BEGIN PUBLIC KEY-----')) {
            return {
                isValid: false,
                error: 'File does not appear to contain a valid certificate or public key'
            };
        }

        return {
            isValid: true,
            error: null,
            sanitizedContent: trimmedContent + '\n'
        };
    }
}

module.exports = new ValidationUtils();
