class SealedSecretManager {
    constructor() {
        this.apiBase = '/api';
        this.currentTransformMode = 'dataToString'; // Default mode
        this.isLoading = false;
        this.currentPage = null; // Track current page for localStorage management
        this.initializeEventListeners();
        this.initializeRouting();
        this.checkServerHealth();
        this.loadClusters();
        this.initializeAnimations();
        this.loadFromLocalStorage();
    }

    initializeAnimations() {
        // Add smooth scroll behavior
        document.documentElement.style.scrollBehavior = 'smooth';
        
        // Add intersection observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        
        // Observe all form containers and cards
        document.querySelectorAll('.form-container, .admin-container, .generator-card, .cluster-card').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'all 0.6s ease-out';
            observer.observe(el);
        });
    }

    initializeEventListeners() {
        // Navigation
        document.getElementById('userPageBtn').addEventListener('click', () => this.showPage('user'));
        document.getElementById('transformPageBtn').addEventListener('click', () => this.showPage('transform'));
        document.getElementById('base64PageBtn').addEventListener('click', () => this.showPage('base64'));
        document.getElementById('randomPageBtn').addEventListener('click', () => this.showPage('random'));
        document.getElementById('notesPageBtn').addEventListener('click', () => this.showPage('notes'));
        document.getElementById('adminPageBtn').addEventListener('click', () => this.showPage('admin'));

        // User page - Sealed Secrets
        document.getElementById('convertBtn').addEventListener('click', () => this.convertToSealedSecret());
        document.getElementById('copyBtn').addEventListener('click', () => this.copyToClipboard('sealedSecretOutput'));
        document.getElementById('insertSampleBtn').addEventListener('click', () => this.insertSampleSecret());

        // Base64 tools
        document.getElementById('encodeBtn').addEventListener('click', () => this.encodeBase64());
        document.getElementById('decodeBtn').addEventListener('click', () => this.decodeBase64());
        document.getElementById('copyEncodedBtn').addEventListener('click', () => this.copyToClipboard('encodedOutput'));
        document.getElementById('copyDecodedBtn').addEventListener('click', () => this.copyToClipboard('decodedOutput'));

        // Secret transformation tools
        document.getElementById('dataToStringModeBtn').addEventListener('click', () => this.setTransformMode('dataToString'));
        document.getElementById('stringToDataModeBtn').addEventListener('click', () => this.setTransformMode('stringToData'));
        document.getElementById('transformBtn').addEventListener('click', () => this.performTransformation());
        document.getElementById('copyTransformBtn').addEventListener('click', () => this.copyToClipboard('transformOutput'));
        document.getElementById('insertSampleBtn').addEventListener('click', () => this.insertTransformSample());

        // Notes page
        document.getElementById('createNoteForm').addEventListener('submit', (e) => this.handleCreateNote(e));
        document.getElementById('copyLinkBtn').addEventListener('click', () => this.copyGeneratedLink());

        // Random page
        document.getElementById('generatePasswordBtn').addEventListener('click', () => this.generatePassword());
        document.getElementById('copyPasswordBtn').addEventListener('click', () => this.copyToClipboard('passwordOutput'));
        document.getElementById('generateUuidBtn').addEventListener('click', () => this.generateUuid());
        document.getElementById('copyUuidBtn').addEventListener('click', () => this.copyToClipboard('uuidOutput'));
        document.getElementById('generateNumberBtn').addEventListener('click', () => this.generateRandomNumber());
        document.getElementById('copyNumberBtn').addEventListener('click', () => this.copyToClipboard('numberOutput'));
        document.getElementById('generateStringBtn').addEventListener('click', () => this.generateRandomString());
        document.getElementById('copyStringBtn').addEventListener('click', () => this.copyToClipboard('stringOutput'));


        // Auto-resize textareas when count changes (only if there's content)
        document.getElementById('passwordCount').addEventListener('input', () => this.smartResizeTextarea('passwordOutput'));
        document.getElementById('uuidCount').addEventListener('input', () => this.smartResizeTextarea('uuidOutput'));

        // Auto-resize YAML textareas on input
        document.getElementById('secretInput').addEventListener('input', () => {
            this.autoResizeTextarea('secretInput');
            this.saveToLocalStorage();
        });
        document.getElementById('sealedSecretOutput').addEventListener('input', () => this.autoResizeTextarea('sealedSecretOutput'));
        
        // Auto-resize transform textareas
        const transformInput = document.getElementById('transformInput');
        const transformOutput = document.getElementById('transformOutput');
        if (transformInput) {
            transformInput.addEventListener('input', () => {
                this.autoResizeTextarea('transformInput');
                this.saveToLocalStorage();
            });
        }
        if (transformOutput) {
            transformOutput.addEventListener('input', () => this.autoResizeTextarea('transformOutput'));
        }

        // Auto-resize base64 textareas
        const textToEncode = document.getElementById('textToEncode');
        const encodedOutput = document.getElementById('encodedOutput');
        const textToDecode = document.getElementById('textToDecode');
        const decodedOutput = document.getElementById('decodedOutput');
        
        if (textToEncode) {
            textToEncode.addEventListener('input', () => {
                this.autoResizeTextarea('textToEncode');
                this.saveToLocalStorage();
            });
        }
        if (encodedOutput) {
            encodedOutput.addEventListener('input', () => this.autoResizeTextarea('encodedOutput'));
        }
        if (textToDecode) {
            textToDecode.addEventListener('input', () => {
                this.autoResizeTextarea('textToDecode');
                this.saveToLocalStorage();
            });
        }
        if (decodedOutput) {
            decodedOutput.addEventListener('input', () => this.autoResizeTextarea('decodedOutput'));
        }

        // Auto-resize notes textarea
        const noteContent = document.getElementById('noteContent');
        if (noteContent) {
            noteContent.addEventListener('input', () => {
                this.autoResizeTextarea('noteContent');
                this.saveToLocalStorage();
            });
        }

        // Sliders
        document.getElementById('passwordLength').addEventListener('input', (e) => this.updateSliderValue('passwordLength', 'passwordLengthValue'));
        document.getElementById('passwordCount').addEventListener('input', (e) => this.updateSliderValue('passwordCount', 'passwordCountValue'));
        document.getElementById('uuidCount').addEventListener('input', (e) => this.updateSliderValue('uuidCount', 'uuidCountValue'));

        // Admin page
        document.getElementById('addClusterForm').addEventListener('submit', (e) => this.handleAddCluster(e));
        
        // Certificate input method toggle
        document.getElementById('fileMethodBtn').addEventListener('click', () => this.switchCertInputMethod('file'));
        document.getElementById('textMethodBtn').addEventListener('click', () => this.switchCertInputMethod('text'));

        // Add localStorage listeners for other form fields
        document.getElementById('clusterSelect').addEventListener('change', () => this.saveToLocalStorage());
        document.getElementById('namespaceInput').addEventListener('input', () => this.saveToLocalStorage());
        document.getElementById('noteTitle').addEventListener('input', () => this.saveToLocalStorage());
        
        // Clear local data button
        document.getElementById('clearLocalDataBtn').addEventListener('click', () => this.handleClearLocalData());

    }

    initializeRouting() {
        // Listen for hash changes (browser back/forward)
        window.addEventListener('hashchange', () => this.handleRouteChange());
        
        // Initialize page based on current hash
        this.handleRouteChange();
    }

    handleRouteChange() {
        const hash = window.location.hash.slice(1); // Remove the # symbol
        const pageMap = {
            'sealed-secrets': 'user',
            'transform': 'transform',
            'base64': 'base64',
            'generator': 'random',
            'notes': 'notes',
            'settings': 'admin'
        };
        
        const page = pageMap[hash] || 'user'; // Default to user page
        this.showPage(page, false); // false = don't update URL to avoid loop
    }

    showPage(page, updateUrl = true) {
        // Clear localStorage when switching pages
        if (this.currentPage && this.currentPage !== page) {
            this.clearLocalStorage();
        }
        
        // Update current page tracking
        this.currentPage = page;
        
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => btn.classList.remove('active'));
        document.getElementById(`${page}PageBtn`).classList.add('active');

        // Update pages
        document.querySelectorAll('.page').forEach(p => p.classList.remove('active'));
        document.getElementById(`${page}Page`).classList.add('active');
        
        // Load saved data for the new page
        this.loadFromLocalStorage();
        
        // Update URL hash if requested
        if (updateUrl) {
            const hashMap = {
                'user': 'sealed-secrets',
                'transform': 'transform',
                'base64': 'base64',
                'random': 'generator',
                'notes': 'notes',
                'admin': 'settings'
            };
            const newHash = hashMap[page] || 'sealed-secrets';
            window.location.hash = newHash;
        }
    }

    switchCertInputMethod(method) {
        // Update buttons
        document.querySelectorAll('.method-option').forEach(btn => btn.classList.remove('active'));
        document.getElementById(`${method}MethodBtn`).classList.add('active');

        // Show/hide input methods
        if (method === 'file') {
            document.getElementById('fileMethod').classList.remove('hidden');
            document.getElementById('textMethod').classList.add('hidden');
            // Clear text input
            document.getElementById('certContent').value = '';
        } else {
            document.getElementById('fileMethod').classList.add('hidden');
            document.getElementById('textMethod').classList.remove('hidden');
            // Clear file input
            document.getElementById('certFile').value = '';
        }
    }

    async checkServerHealth() {
        try {
            const response = await fetch(`${this.apiBase}/health`);
            const health = await response.json();
            
            if (health.kubeseal !== 'installed') {
                this.showMessage('Warning: Kubeseal is not installed on the server. Sealed secret conversion will not work.', 'error');
            }
            

        } catch (error) {
            this.showMessage('Cannot connect to server. Please ensure the server is running.', 'error');
            console.error('Health check failed:', error);
        }
    }

    async loadClusters() {
        try {
            const response = await fetch(`${this.apiBase}/clusters`);
            if (!response.ok) throw new Error('Failed to load clusters');
            
            const clusters = await response.json();
            this.renderClustersList(clusters);
            this.updateClusterDropdown(clusters);
        } catch (error) {
            console.error('Error loading clusters:', error);
            this.showMessage('Failed to load clusters', 'error');
        }
    }

    async handleAddCluster(event) {
        event.preventDefault();
        
        const name = document.getElementById('clusterName').value.trim();
        const certFile = document.getElementById('certFile').files[0];
        const certContent = document.getElementById('certContent').value.trim();
        
        if (!name) {
            this.showMessage('Please enter a cluster name', 'error');
            return;
        }
        
        if (!certFile && !certContent) {
            this.showMessage('Please provide a certificate file or paste certificate content', 'error');
            return;
        }
        
        const formData = new FormData();
        formData.append('name', name);
        
        if (certFile) {
            formData.append('certificate', certFile);
        } else if (certContent) {
            formData.append('certContent', certContent);
        }
        
        const addBtn = document.getElementById('addClusterBtn');
        const originalText = addBtn.textContent;
        addBtn.disabled = true;
        addBtn.textContent = 'Adding...';
        
        try {
            const response = await fetch(`${this.apiBase}/clusters`, {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.error || 'Failed to add cluster');
            }
            
            // Clear form
            document.getElementById('addClusterForm').reset();
            document.getElementById('certContent').value = '';
            
            // Reset to text method by default
            this.switchCertInputMethod('text');
            
            // Reload clusters
            await this.loadClusters();
            
            this.showMessage('Cluster added successfully', 'success');
            
        } catch (error) {
            console.error('Error adding cluster:', error);
            this.showMessage(error.message, 'error');
        } finally {
            addBtn.disabled = false;
            addBtn.textContent = originalText;
        }
    }

    startEditCluster(id) {
        const clusterItem = document.querySelector(`[data-cluster-id="${id}"]`);
        if (!clusterItem) return;

        const nameDisplay = clusterItem.querySelector('.cluster-name');
        const actionsContainer = clusterItem.querySelector('.cluster-compact-actions');
        const originalName = nameDisplay.textContent;

        // Create input field
        const nameInput = document.createElement('input');
        nameInput.type = 'text';
        nameInput.className = 'cluster-name-input';
        nameInput.value = originalName;
        nameInput.style.cssText = `
            border: 2px solid var(--primary-500);
            border-radius: var(--radius-sm);
            padding: var(--space-1) var(--space-2);
            font-size: var(--font-size-sm);
            font-weight: 600;
            background: white;
            outline: none;
            width: 150px;
        `;

        // Create edit actions
        const editActions = document.createElement('div');
        editActions.className = 'edit-actions';
        editActions.innerHTML = `
            <button class="save-btn" onclick="window.manager.saveClusterEdit('${id}')" title="Save">
                ✅
            </button>
            <button class="cancel-btn" onclick="window.manager.cancelClusterEdit('${id}')" title="Cancel">
                ❌
            </button>
        `;
        editActions.style.cssText = `
            display: flex;
            gap: var(--space-1);
        `;

        // Replace display with input
        nameDisplay.style.display = 'none';
        nameDisplay.parentNode.insertBefore(nameInput, nameDisplay);

        // Replace actions with edit actions
        actionsContainer.style.display = 'none';
        clusterItem.appendChild(editActions);

        // Focus and select text
        setTimeout(() => {
            nameInput.focus();
            nameInput.select();
        }, 50);

        // Add keyboard event listeners
        const keyHandler = (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.saveClusterEdit(id);
            } else if (e.key === 'Escape') {
                e.preventDefault();
                this.cancelClusterEdit(id);
            }
        };
        nameInput.addEventListener('keydown', keyHandler);

        // Store references for cleanup
        clusterItem._nameInput = nameInput;
        clusterItem._editActions = editActions;
        clusterItem._keyHandler = keyHandler;
        clusterItem._originalName = originalName;

        // Add editing state
        clusterItem.classList.add('editing');
    }

    autoSizeInput(input) {
        // Làm cái input box rất rộng luôn cho chắc
        input.style.width = '600px';
        input.style.minWidth = '600px';
        input.scrollLeft = 0;
    }

    cancelClusterEdit(id) {
        const clusterItem = document.querySelector(`[data-cluster-id="${id}"]`);
        if (!clusterItem) return;

        const nameDisplay = clusterItem.querySelector('.cluster-name');
        const actionsContainer = clusterItem.querySelector('.cluster-compact-actions');

        // Remove input field
        if (clusterItem._nameInput) {
            clusterItem._nameInput.remove();
            delete clusterItem._nameInput;
        }

        // Remove edit actions
        if (clusterItem._editActions) {
            clusterItem._editActions.remove();
            delete clusterItem._editActions;
        }

        // Clean up event handlers
        if (clusterItem._keyHandler) {
            clusterItem._nameInput?.removeEventListener('keydown', clusterItem._keyHandler);
            delete clusterItem._keyHandler;
        }

        // Restore display and actions
        nameDisplay.style.display = '';
        actionsContainer.style.display = '';

        // Remove editing state
        clusterItem.classList.remove('editing');
    }

    async saveClusterEdit(id) {
        const clusterItem = document.querySelector(`[data-cluster-id="${id}"]`);
        if (!clusterItem) return;

        const nameInput = clusterItem._nameInput;
        const newName = nameInput.value.trim();
        const originalName = clusterItem._originalName;

        if (!newName) {
            this.showMessage('Cluster name cannot be empty', 'error');
            nameInput.focus();
            return;
        }

        if (newName === originalName) {
            this.cancelClusterEdit(id);
            return;
        }

        const saveBtn = clusterItem._editActions?.querySelector('.save-btn');
        if (saveBtn) {
            saveBtn.disabled = true;
            saveBtn.textContent = '⏳';
        }

        try {
            const formData = new FormData();
            formData.append('name', newName);
            
            const response = await fetch(`${this.apiBase}/clusters/${id}`, {
                method: 'PUT',
                body: formData
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.error || 'Failed to update cluster');
            }
            
            await this.loadClusters();
            this.showMessage('Cluster updated successfully', 'success');
            
        } catch (error) {
            console.error('Error updating cluster:', error);
            this.showMessage(error.message, 'error');
            
            if (saveBtn) {
                saveBtn.disabled = false;
                saveBtn.textContent = '✅';
            }
        }
    }

    startDeleteCluster(id) {
        const clusterItem = document.querySelector(`[data-cluster-id="${id}"]`);
        if (!clusterItem) return;

        const actionsContainer = clusterItem.querySelector('.cluster-compact-actions');
        const originalActions = actionsContainer.innerHTML;

        // Create delete confirmation actions
        const deleteActions = document.createElement('div');
        deleteActions.className = 'delete-actions';
        deleteActions.innerHTML = `
            <button class="confirm-delete-btn" onclick="window.manager.confirmDeleteCluster('${id}')" title="Confirm Delete">
                ⚠️
            </button>
            <button class="cancel-delete-btn" onclick="window.manager.cancelDeleteCluster('${id}')" title="Cancel">
                ❌
            </button>
        `;
        deleteActions.style.cssText = `
            display: flex;
            gap: var(--space-1);
        `;

        // Replace actions with delete actions
        actionsContainer.style.display = 'none';
        clusterItem.appendChild(deleteActions);

        // Store references for cleanup
        clusterItem._deleteActions = deleteActions;
        clusterItem._originalActions = originalActions;

        // Add deleting state
        clusterItem.classList.add('deleting');
    }

    cancelDeleteCluster(id) {
        const clusterItem = document.querySelector(`[data-cluster-id="${id}"]`);
        if (!clusterItem) return;

        const actionsContainer = clusterItem.querySelector('.cluster-compact-actions');

        // Remove delete actions
        if (clusterItem._deleteActions) {
            clusterItem._deleteActions.remove();
            delete clusterItem._deleteActions;
        }

        // Restore original actions
        actionsContainer.style.display = '';

        // Remove deleting state
        clusterItem.classList.remove('deleting');
    }

    async confirmDeleteCluster(id) {
        const clusterCard = document.querySelector(`[data-cluster-id="${id}"]`);
        if (!clusterCard) return;

        const confirmBtn = clusterCard.querySelector('.confirm-delete-btn');
        const originalText = confirmBtn.innerHTML;
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<span class="btn-icon">⏳</span>Deleting...';

        try {
            const response = await fetch(`${this.apiBase}/clusters/${id}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.error || 'Failed to delete cluster');
            }
            
            await this.loadClusters();
            this.showMessage('Cluster deleted successfully', 'success');
            
        } catch (error) {
            console.error('Error deleting cluster:', error);
            this.showMessage(error.message, 'error');
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = originalText;
        }
    }

    renderClustersList(clusters) {
        const container = document.getElementById('clustersList');
        
        if (clusters.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">🏗️</div>
                    <div class="empty-state-title">No clusters configured</div>
                    <div class="empty-state-description">Add your first Kubernetes cluster to start creating sealed secrets</div>
                </div>
            `;
            return;
        }

        container.innerHTML = clusters.map(cluster => `
            <div class="cluster-compact-item" data-cluster-id="${cluster.id}">
                <div class="cluster-compact-info">
                    <div class="cluster-compact-avatar">
                        ${cluster.name.charAt(0).toUpperCase()}
                    </div>
                    <div class="cluster-compact-details">
                        <h4 class="cluster-name">${this.escapeHtml(cluster.name)}</h4>
                        <p>${cluster.hasCertificate ? 'Certificate configured' : 'Certificate missing'}</p>
                    </div>
                </div>
                <div class="cluster-compact-actions">
                    <button class="edit-btn" onclick="window.manager.startEditCluster('${cluster.id}')" title="Edit cluster">
                        ✏️
                    </button>
                    <button class="delete-btn" onclick="window.manager.startDeleteCluster('${cluster.id}')" title="Delete cluster">
                        🗑️
                    </button>
                </div>
            </div>
        `).join('');
    }



    updateClusterDropdown(clusters) {
        const select = document.getElementById('clusterSelect');
        const currentValue = select.value;
        
        select.innerHTML = '<option value="">Choose a cluster...</option>';
        
        clusters.forEach(cluster => {
            const option = document.createElement('option');
            option.value = cluster.id;
            option.textContent = cluster.name;
            select.appendChild(option);
        });

        // Restore selection if it still exists
        if (currentValue && clusters.find(c => c.id === currentValue)) {
            select.value = currentValue;
        }
    }

    insertSampleSecret() {
        const sampleSecret = `apiVersion: v1
kind: Secret
metadata:
  name: my-secret
  # namespace will be added automatically if not specified
type: Opaque
data:
  username: dXNlcm5hbWU=  # base64 encoded 'username'
  password: cGFzc3dvcmQ=  # base64 encoded 'password'
stringData:
  # You can also use stringData for plain text values
  # api-key: your-api-key-here
  # database-url: ********************************/db`;

        document.getElementById('secretInput').value = sampleSecret;
        this.autoResizeTextarea('secretInput');
        this.showMessage('Sample secret inserted. Edit as needed for your use case.', 'success');
    }

    async convertToSealedSecret() {
        const secretInput = document.getElementById('secretInput').value.trim();
        const clusterSelect = document.getElementById('clusterSelect');
        const namespaceInput = document.getElementById('namespaceInput').value.trim();
        const outputTextarea = document.getElementById('sealedSecretOutput');
        const convertBtn = document.getElementById('convertBtn');

        if (!secretInput) {
            this.showMessage('Please enter a secret YAML', 'error');
            return;
        }

        if (!clusterSelect.value) {
            this.showMessage('Please select a cluster', 'error');
            return;
        }

        if (this.isLoading) return;

        try {
            this.setLoadingState(convertBtn, true, 'Converting...', '🔄');
            this.isLoading = true;
            outputTextarea.value = '';

            const requestBody = {
                secretYaml: secretInput,
                clusterId: clusterSelect.value
            };

            // Add target namespace if specified
            if (namespaceInput) {
                requestBody.targetNamespace = namespaceInput;
            }

            const response = await fetch(`${this.apiBase}/convert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Conversion failed');
            }

            outputTextarea.value = result.sealedSecret;
            this.autoResizeTextarea('sealedSecretOutput');
            this.showMessage(`Secret converted successfully for cluster: ${result.clusterName} (namespace: ${result.namespace})`, 'success');
            
            // Add success animation
            outputTextarea.style.animation = 'pulse 0.6s ease-in-out';
            setTimeout(() => {
                outputTextarea.style.animation = '';
            }, 600);

        } catch (error) {
            console.error('Error converting secret:', error);
            this.showMessage(error.message, 'error');
            outputTextarea.value = '';
        } finally {
            this.setLoadingState(convertBtn, false, '🔒 Convert to Sealed Secret');
            this.isLoading = false;
        }
    }

    setLoadingState(button, isLoading, text, icon = '') {
        if (isLoading) {
            button.disabled = true;
            button.innerHTML = `<span class="loading-spinner"></span> ${text}`;
            button.classList.add('loading');
        } else {
            button.disabled = false;
            button.innerHTML = text;
            button.classList.remove('loading');
        }
    }

    async encodeBase64() {
        const textInput = document.getElementById('textToEncode');
        const outputTextarea = document.getElementById('encodedOutput');
        const encodeBtn = document.getElementById('encodeBtn');
        
        const text = textInput.value.trim();
        if (!text) {
            this.showMessage('Please enter text to encode', 'error');
            return;
        }
        
        encodeBtn.disabled = true;
        encodeBtn.textContent = 'Encoding...';
        
        try {
            const response = await fetch(`${this.apiBase}/base64/encode`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text })
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.error || 'Encoding failed');
            }
            
            outputTextarea.value = result.encoded;
            this.autoResizeTextarea('encodedOutput');
            this.showMessage('Text encoded successfully', 'success');
            
        } catch (error) {
            console.error('Error encoding base64:', error);
            this.showMessage(error.message, 'error');
        } finally {
            encodeBtn.disabled = false;
            encodeBtn.textContent = 'Encode';
        }
    }

    async decodeBase64() {
        const textInput = document.getElementById('textToDecode');
        const outputTextarea = document.getElementById('decodedOutput');
        const decodeBtn = document.getElementById('decodeBtn');
        
        const encoded = textInput.value.trim();
        if (!encoded) {
            this.showMessage('Please enter base64 text to decode', 'error');
            return;
        }
        
        decodeBtn.disabled = true;
        decodeBtn.textContent = 'Decoding...';
        
        try {
            const response = await fetch(`${this.apiBase}/base64/decode`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ encoded })
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.error || 'Decoding failed');
            }
            
            outputTextarea.value = result.decoded;
            this.autoResizeTextarea('decodedOutput');
            this.showMessage('Base64 decoded successfully', 'success');
            
        } catch (error) {
            console.error('Error decoding base64:', error);
            this.showMessage(error.message, 'error');
        } finally {
            decodeBtn.disabled = false;
            decodeBtn.textContent = 'Decode';
        }
    }

    setTransformMode(mode) {
        this.currentTransformMode = mode;
        
        // Update toggle buttons
        document.querySelectorAll('.transform-option').forEach(btn => btn.classList.remove('active'));
        document.getElementById(`${mode}ModeBtn`).classList.add('active');
        
        // Update UI elements based on mode
        const inputLabel = document.getElementById('inputLabel');
        const outputLabel = document.getElementById('outputLabel');
        const transformBtn = document.getElementById('transformBtn');
        const transformInput = document.getElementById('transformInput');
        
        if (mode === 'dataToString') {
            inputLabel.textContent = 'Secret with data (base64):';
            outputLabel.textContent = 'Result (stringData format):';
            transformBtn.innerHTML = '🔓 Convert to stringData';
            transformInput.placeholder = `apiVersion: v1
kind: Secret
metadata:
  name: my-secret
type: Opaque
data:
  username: dXNlcm5hbWU=
  password: cGFzc3dvcmQ=`;
        } else {
            inputLabel.textContent = 'Secret with stringData (plain text):';
            outputLabel.textContent = 'Result (data format):';
            transformBtn.innerHTML = '🔒 Convert to data';
            transformInput.placeholder = `apiVersion: v1
kind: Secret
metadata:
  name: my-secret
type: Opaque
stringData:
  username: myuser
  password: mypassword`;
        }
        
        // Clear input and output
        transformInput.value = '';
        document.getElementById('transformOutput').value = '';
        this.saveToLocalStorage();
    }

    insertTransformSample() {
        const transformInput = document.getElementById('transformInput');
        
        if (this.currentTransformMode === 'dataToString') {
            const dataSample = `apiVersion: v1
kind: Secret
metadata:
  name: my-secret
type: Opaque
data:
  username: dXNlcm5hbWU=  # base64 for 'username'
  password: cGFzc3dvcmQ=  # base64 for 'password'
  api-key: bXktYXBpLWtleQ==  # base64 for 'my-api-key'`;
            transformInput.value = dataSample;
            this.autoResizeTextarea('transformInput');
            this.showMessage('Sample secret with data (base64) inserted', 'success');
        } else {
            const stringDataSample = `apiVersion: v1
kind: Secret
metadata:
  name: my-secret
type: Opaque
stringData:
  username: myuser
  password: mypassword
  api-key: my-secret-api-key
  database-url: ********************************/db`;
            transformInput.value = stringDataSample;
            this.autoResizeTextarea('transformInput');
            this.showMessage('Sample secret with stringData (plain text) inserted', 'success');
        }
    }

    async performTransformation() {
        const secretInput = document.getElementById('transformInput').value.trim();
        const outputTextarea = document.getElementById('transformOutput');
        const transformBtn = document.getElementById('transformBtn');

        if (!secretInput) {
            const fieldType = this.currentTransformMode === 'dataToString' ? 'data' : 'stringData';
            this.showMessage(`Please enter a secret YAML with ${fieldType} field`, 'error');
            return;
        }

        const originalText = transformBtn.innerHTML;
        transformBtn.disabled = true;
        transformBtn.textContent = 'Converting...';
        outputTextarea.value = '';

        try {
            const endpoint = this.currentTransformMode === 'dataToString' 
                ? '/secret/data-to-stringdata' 
                : '/secret/stringdata-to-data';

            const response = await fetch(`${this.apiBase}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ secretYaml: secretInput })
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Transformation failed');
            }

            outputTextarea.value = result.transformedSecret;
            this.autoResizeTextarea('transformOutput');
            this.showMessage(result.message || 'Secret transformed successfully', 'success');

        } catch (error) {
            console.error('Error transforming secret:', error);
            this.showMessage(error.message, 'error');
            outputTextarea.value = '';
        } finally {
            transformBtn.disabled = false;
            transformBtn.innerHTML = originalText;
        }
    }

    async handleCreateNote(event) {
        event.preventDefault();
        
        const title = document.getElementById('noteTitle').value.trim();
        const content = document.getElementById('noteContent').value.trim();
        
        if (!content) {
            this.showMessage('Please enter note content', 'error');
            return;
        }
        
        const createBtn = document.getElementById('createNoteBtn');
        const originalHtml = createBtn.innerHTML;
        createBtn.disabled = true;
        createBtn.innerHTML = '<span class="btn-icon">⏳</span>Creating...';
        
        try {
            const response = await fetch(`${this.apiBase}/notes`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ title, content })
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.error || 'Failed to create note');
            }
            
            // Clear form
            document.getElementById('createNoteForm').reset();
            this.saveToLocalStorage();
            
            // Show generated link
            this.showGeneratedLink(result.link, result.expiresAt);
            this.showMessage('Secure note created successfully!', 'success');
            
        } catch (error) {
            console.error('Error creating note:', error);
            this.showMessage(error.message, 'error');
        } finally {
            createBtn.disabled = false;
            createBtn.innerHTML = originalHtml;
        }
    }
    
    showGeneratedLink(link, expiresAt) {
        const linkSection = document.getElementById('generatedLinkSection');
        const linkInput = document.getElementById('generatedLink');
        const expirySpan = document.getElementById('expiryTime');
        
        linkInput.value = link;
        
        const expiryDate = new Date(expiresAt);
        expirySpan.textContent = expiryDate.toLocaleString();
        
        linkSection.classList.remove('hidden');
        
        // Scroll to the generated link section
        linkSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
    
    async copyGeneratedLink() {
        const linkInput = document.getElementById('generatedLink');
        const copyBtn = document.getElementById('copyLinkBtn');
        
        if (!linkInput.value) {
            return;
        }

        try {
            await navigator.clipboard.writeText(linkInput.value);
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<span class="btn-icon">✅</span>Copied!';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        } catch (error) {
            // Fallback for older browsers
            linkInput.select();
            document.execCommand('copy');
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<span class="btn-icon">✅</span>Copied!';
            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
            }, 2000);
        }
    }

    async copyToClipboard(elementId) {
        const element = document.getElementById(elementId);
        let copyBtn;
        
        switch (elementId) {
            case 'sealedSecretOutput':
                copyBtn = document.getElementById('copyBtn');
                break;
            case 'encodedOutput':
                copyBtn = document.getElementById('copyEncodedBtn');
                break;
            case 'decodedOutput':
                copyBtn = document.getElementById('copyDecodedBtn');
                break;
            case 'transformOutput':
                copyBtn = document.getElementById('copyTransformBtn');
                break;
            case 'passwordOutput':
                copyBtn = document.getElementById('copyPasswordBtn');
                break;
            case 'uuidOutput':
                copyBtn = document.getElementById('copyUuidBtn');
                break;
            case 'numberOutput':
                copyBtn = document.getElementById('copyNumberBtn');
                break;
            case 'stringOutput':
                copyBtn = document.getElementById('copyStringBtn');
                break;

            default:
                copyBtn = document.getElementById('copyBtn');
        }
        
        if (!element.value) {
            return;
        }

        try {
            await navigator.clipboard.writeText(element.value);
            const originalText = copyBtn.textContent;
            copyBtn.textContent = 'Copied!';
            setTimeout(() => {
                copyBtn.textContent = originalText;
            }, 2000);
        } catch (error) {
            // Fallback for older browsers
            element.select();
            document.execCommand('copy');
            const originalText = copyBtn.textContent;
            copyBtn.textContent = 'Copied!';
            setTimeout(() => {
                copyBtn.textContent = originalText;
            }, 2000);
        }
    }

    showMessage(message, type) {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.toast-notification');
        existingMessages.forEach(msg => {
            msg.classList.remove('show');
            setTimeout(() => msg.remove(), 300);
        });

        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        
        const icon = this.getToastIcon(type);
        const content = `
            <div class="toast-content">
                <div class="toast-icon">${icon}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">×</button>
        `;
        
        toast.innerHTML = content;
        document.body.appendChild(toast);

        // Show toast with animation
        requestAnimationFrame(() => {
            toast.classList.add('show');
        });

        // Auto-hide after 5 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 5000);
    }

    getToastIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }



    // Random generation methods
    generatePassword() {
        const length = parseInt(document.getElementById('passwordLength').value) || 16;
        const count = parseInt(document.getElementById('passwordCount').value) || 1;
        const includeUppercase = document.getElementById('includeUppercase').checked;
        const includeLowercase = document.getElementById('includeLowercase').checked;
        const includeNumbers = document.getElementById('includeNumbers').checked;
        const includeSymbols = document.getElementById('includeSymbols').checked;
        const excludeSimilar = document.getElementById('excludeSimilar').checked;

        if (!includeUppercase && !includeLowercase && !includeNumbers && !includeSymbols) {
            document.getElementById('passwordOutput').value = 'Error: Please select at least one character type';
            return;
        }

        if (length < 4 || length > 128) {
            document.getElementById('passwordOutput').value = 'Error: Password length must be between 4 and 128 characters';
            return;
        }

        if (count < 1 || count > 10) {
            document.getElementById('passwordOutput').value = 'Error: Generate count must be between 1 and 10';
            return;
        }

        let charset = '';
        if (includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        if (includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
        if (includeNumbers) charset += '0123456789';
        if (includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

        if (excludeSimilar) {
            charset = charset.replace(/[0Ol1I]/g, '');
        }

        const passwords = [];
        for (let i = 0; i < count; i++) {
            let password = '';
            for (let j = 0; j < length; j++) {
                password += charset.charAt(Math.floor(Math.random() * charset.length));
            }
            passwords.push(password);
        }

        document.getElementById('passwordOutput').value = passwords.join('\n');
        this.autoResizeTextarea('passwordOutput');
    }

    generateUuid() {
        const version = document.getElementById('uuidVersion').value;
        const count = parseInt(document.getElementById('uuidCount').value) || 1;
        const uppercase = document.getElementById('uuidUppercase').checked;
        const noDashes = document.getElementById('uuidNoDashes').checked;

        if (count < 1 || count > 10) {
            document.getElementById('uuidOutput').value = 'Error: Generate count must be between 1 and 10';
            return;
        }

        const uuids = [];
        for (let i = 0; i < count; i++) {
            let uuid;
            if (version === 'v4') {
                uuid = this.generateUuidV4();
            } else if (version === 'v1') {
                uuid = this.generateUuidV1();
            }

            if (noDashes) {
                uuid = uuid.replace(/-/g, '');
            }
            if (uppercase) {
                uuid = uuid.toUpperCase();
            }

            uuids.push(uuid);
        }

        document.getElementById('uuidOutput').value = uuids.join('\n');
        this.autoResizeTextarea('uuidOutput');
    }

    generateUuidV4() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    generateUuidV1() {
        // Simplified UUID v1 implementation
        const timestamp = Date.now();
        const clockSeq = Math.floor(Math.random() * 16384);
        const node = Math.random().toString(16).substring(2, 14);

        const timeLow = (timestamp & 0xffffffff).toString(16).padStart(8, '0');
        const timeMid = ((timestamp >> 32) & 0xffff).toString(16).padStart(4, '0');
        const timeHigh = (((timestamp >> 48) & 0x0fff) | 0x1000).toString(16).padStart(4, '0');
        const clockSeqHigh = ((clockSeq >> 8) | 0x80).toString(16).padStart(2, '0');
        const clockSeqLow = (clockSeq & 0xff).toString(16).padStart(2, '0');

        return `${timeLow}-${timeMid}-${timeHigh}-${clockSeqHigh}${clockSeqLow}-${node}`;
    }

    generateRandomNumber() {
        const min = parseInt(document.getElementById('randomMin').value) || 1;
        const max = parseInt(document.getElementById('randomMax').value) || 100;

        if (min >= max) {
            document.getElementById('numberOutput').value = 'Error: Min must be less than max';
            return;
        }

        const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min;
        document.getElementById('numberOutput').value = randomNumber;
    }

    generateRandomString() {
        const length = parseInt(document.getElementById('stringLength').value) || 8;
        const type = document.getElementById('stringType').value;

        if (length < 1 || length > 100) {
            document.getElementById('stringOutput').value = 'Error: Length must be 1-100';
            return;
        }

        let charset = '';
        switch (type) {
            case 'alphanumeric':
                charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                break;
            case 'alphabetic':
                charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
                break;
            case 'numeric':
                charset = '0123456789';
                break;
            case 'hex':
                charset = '0123456789abcdef';
                break;
        }

        let result = '';
        for (let i = 0; i < length; i++) {
            result += charset.charAt(Math.floor(Math.random() * charset.length));
        }

        document.getElementById('stringOutput').value = result;
    }



    autoResizeTextarea(elementId) {
        const textarea = document.getElementById(elementId);
        if (!textarea) return;

        // Reset height to auto to get the correct scrollHeight
        textarea.style.height = 'auto';
        
        // Calculate new height based on content
        const newHeight = Math.max(textarea.scrollHeight, textarea.offsetHeight);
        
        // Set the new height
        textarea.style.height = newHeight + 'px';
        
        // Ensure minimum height
        const minHeight = parseInt(getComputedStyle(textarea).minHeight) || 120;
        if (newHeight < minHeight) {
            textarea.style.height = minHeight + 'px';
        }
    }

    resetTextareaHeight(elementId) {
        const textarea = document.getElementById(elementId);
        if (!textarea) return;
        
        textarea.style.height = 'auto';
        const minHeight = parseInt(getComputedStyle(textarea).minHeight) || 120;
        textarea.style.height = minHeight + 'px';
    }

    smartResizeTextarea(elementId) {
        const textarea = document.getElementById(elementId);
        if (!textarea) return;
        
        if (textarea.value.trim()) {
            this.autoResizeTextarea(elementId);
        } else {
            this.resetTextareaHeight(elementId);
        }
    }

    updateSliderValue(sliderId, valueId) {
        const slider = document.getElementById(sliderId);
        const valueDisplay = document.getElementById(valueId);
        if (slider && valueDisplay) {
            valueDisplay.textContent = slider.value;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    saveToLocalStorage() {
        try {
            if (!this.currentPage) return;
            
            const data = {
                secretInput: document.getElementById('secretInput')?.value || '',
                transformInput: document.getElementById('transformInput')?.value || '',
                textToEncode: document.getElementById('textToEncode')?.value || '',
                textToDecode: document.getElementById('textToDecode')?.value || '',
                noteTitle: document.getElementById('noteTitle')?.value || '',
                noteContent: document.getElementById('noteContent')?.value || '',
                namespaceInput: document.getElementById('namespaceInput')?.value || '',
                clusterSelect: document.getElementById('clusterSelect')?.value || '',
                currentTransformMode: this.currentTransformMode,
                page: this.currentPage
            };
            localStorage.setItem(`sealifyData_${this.currentPage}`, JSON.stringify(data));
        } catch (error) {
            console.log('Failed to save to localStorage:', error);
        }
    }

    loadFromLocalStorage() {
        try {
            if (!this.currentPage) return;
            
            const savedData = localStorage.getItem(`sealifyData_${this.currentPage}`);
            if (!savedData) return;
            
            const data = JSON.parse(savedData);
            
            setTimeout(() => {
                if (data.secretInput && document.getElementById('secretInput')) {
                    document.getElementById('secretInput').value = data.secretInput;
                    this.autoResizeTextarea('secretInput');
                }
                
                if (data.transformInput && document.getElementById('transformInput')) {
                    document.getElementById('transformInput').value = data.transformInput;
                    this.autoResizeTextarea('transformInput');
                }
                
                if (data.textToEncode && document.getElementById('textToEncode')) {
                    document.getElementById('textToEncode').value = data.textToEncode;
                    this.autoResizeTextarea('textToEncode');
                }
                
                if (data.textToDecode && document.getElementById('textToDecode')) {
                    document.getElementById('textToDecode').value = data.textToDecode;
                    this.autoResizeTextarea('textToDecode');
                }
                
                if (data.noteTitle && document.getElementById('noteTitle')) {
                    document.getElementById('noteTitle').value = data.noteTitle;
                }
                
                if (data.noteContent && document.getElementById('noteContent')) {
                    document.getElementById('noteContent').value = data.noteContent;
                    this.autoResizeTextarea('noteContent');
                }
                
                if (data.namespaceInput && document.getElementById('namespaceInput')) {
                    document.getElementById('namespaceInput').value = data.namespaceInput;
                }
                
                if (data.clusterSelect && document.getElementById('clusterSelect')) {
                    document.getElementById('clusterSelect').value = data.clusterSelect;
                }
                
                if (data.currentTransformMode) {
                    this.setTransformMode(data.currentTransformMode);
                }
            }, 100);
        } catch (error) {
            console.log('Failed to load from localStorage:', error);
        }
    }

    clearLocalStorage() {
        try {
            if (this.currentPage) {
                localStorage.removeItem(`sealifyData_${this.currentPage}`);
            }
            // Also clean up old format
            localStorage.removeItem('sealifyData');
        } catch (error) {
            console.log('Failed to clear localStorage:', error);
        }
    }

    handleClearLocalData() {
        if (confirm('Are you sure you want to clear all saved data? This will remove all saved form data from your browser and cannot be undone.')) {
            // Clear all page-specific localStorage data
            const pages = ['user', 'transform', 'base64', 'random', 'notes', 'admin'];
            pages.forEach(page => {
                try {
                    localStorage.removeItem(`sealifyData_${page}`);
                } catch (error) {
                    console.log(`Failed to clear ${page} data:`, error);
                }
            });
            
            // Also clear old format
            localStorage.removeItem('sealifyData');
            
            // Clear all form fields
            const fieldsToCheck = [
                'secretInput',
                'transformInput', 
                'textToEncode',
                'textToDecode',
                'noteTitle',
                'noteContent',
                'namespaceInput'
            ];
            
            fieldsToCheck.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.value = '';
                    this.autoResizeTextarea(fieldId);
                }
            });
            
            // Reset cluster select
            const clusterSelect = document.getElementById('clusterSelect');
            if (clusterSelect) {
                clusterSelect.value = '';
            }
            
            // Reset transform mode to default
            this.setTransformMode('dataToString');
            
            this.showMessage('All saved data has been cleared successfully', 'success');
        }
    }
}

// Initialize the application
const manager = new SealedSecretManager();

// Make manager globally accessible for onclick handlers
window.manager = manager;