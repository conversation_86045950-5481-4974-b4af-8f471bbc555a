{"name": "sealify", "version": "1.0.0", "description": "Simple web tool for managing Kubernetes sealed secrets with kubeseal integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "prod": "NODE_ENV=production node server.js", "install-kubeseal": "node scripts/install-kubeseal.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["kubernetes", "sealed-secrets", "kubeseal", "k8s", "security"], "author": "Platform Tools", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "fs-extra": "^11.1.1", "uuid": "^9.0.0", "js-yaml": "^4.1.0", "winston": "^3.10.0", "escape-html": "^1.0.3", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "file-type": "^18.7.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}