const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const yaml = require('js-yaml');
const winston = require('winston');
const { execSync, spawn } = require('child_process');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const escapeHtml = require('escape-html');
const validationUtils = require('./utils/validation');
const fileValidationUtils = require('./utils/fileValidation');
const xssProtection = require('./utils/xssProtection');

const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    defaultMeta: { service: 'sealify' },
    transports: [
        new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston.transports.File({ filename: 'logs/combined.log' }),
        new winston.transports.Console({
            format: winston.format.simple()
        })
    ]
});

const app = express();
const PORT = process.env.PORT || 3000;

// Ensure required directories exist
const requiredDirs = ['data', 'certs', 'logs', 'tmp'];
requiredDirs.forEach(dir => {
    fs.ensureDirSync(path.join(__dirname, dir));
});

// Enhanced security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"], // Required for inline styles
            scriptSrc: ["'self'", "'unsafe-inline'"], // Required for inline scripts
            scriptSrcAttr: ["'unsafe-inline'"], // Required for onclick handlers
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'", "data:"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
            baseUri: ["'self'"],
            formAction: ["'self'"],
            frameAncestors: ["'none'"],
            upgradeInsecureRequests: null, // Disable for localhost development
        },
        reportOnly: false
    },
    crossOriginEmbedderPolicy: false,
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    },
    noSniff: true,
    xssFilter: true,
    referrerPolicy: { policy: "strict-origin-when-cross-origin" }
}));

// Additional XSS protection middleware
app.use((req, res, next) => {
    // Set additional security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');

    // Remove server information
    res.removeHeader('X-Powered-By');

    next();
});

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: {
        error: 'Too many requests from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
});

const strictLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 20, // Limit conversion requests to 20 per windowMs
    message: {
        error: 'Too many conversion requests from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
});

app.use(limiter);

// Middleware
app.use(cors());
app.use(express.json({ limit: '1mb' })); // Reduced from 10mb for security
app.use(express.urlencoded({ extended: true, limit: '1mb' }));

// XSS protection middleware
app.use(xssProtection.sanitizeRequestBody.bind(xssProtection));
app.use(xssProtection.sanitizeQueryParams.bind(xssProtection));

app.use(express.static(path.join(__dirname)));

// Enhanced multer configuration for secure file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        // Ensure tmp directory exists
        const tmpDir = path.join(__dirname, 'tmp');
        fs.ensureDirSync(tmpDir);
        cb(null, tmpDir);
    },
    filename: (req, file, cb) => {
        // Generate secure filename
        const secureFilename = fileValidationUtils.generateSecureFilename(file.originalname);
        cb(null, secureFilename);
    }
});

const upload = multer({
    storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
        files: 1, // Only allow 1 file
        fields: 1, // Only allow 1 field
        parts: 2 // Limit total parts
    },
    fileFilter: (req, file, cb) => {
        // Basic field name validation
        if (file.fieldname !== 'certificate') {
            return cb(new Error('Unexpected field name'), false);
        }

        // Basic filename validation
        if (!file.originalname || file.originalname.length > 255) {
            return cb(new Error('Invalid filename'), false);
        }

        // Check file extension
        const allowedExtensions = ['.pem', '.crt', '.cert', '.key', '.pub'];
        const fileExtension = path.extname(file.originalname).toLowerCase();
        if (!allowedExtensions.includes(fileExtension)) {
            return cb(new Error(`File extension '${fileExtension}' not allowed`), false);
        }

        // Basic MIME type check (multer provides this)
        const allowedMimeTypes = [
            'application/x-pem-file',
            'application/x-x509-ca-cert',
            'text/plain',
            'application/octet-stream'
        ];

        if (file.mimetype && !allowedMimeTypes.includes(file.mimetype) &&
            !file.mimetype.startsWith('text/')) {
            return cb(new Error(`MIME type '${file.mimetype}' not allowed`), false);
        }

        cb(null, true);
    }
});

// Data storage paths
const CLUSTERS_FILE = path.join(__dirname, 'data', 'clusters.json');
const NOTES_FILE = path.join(__dirname, 'data', 'notes.json');

// Initialize data files
if (!fs.existsSync(CLUSTERS_FILE)) {
    fs.writeJsonSync(CLUSTERS_FILE, []);
}

if (!fs.existsSync(NOTES_FILE)) {
    fs.writeJsonSync(NOTES_FILE, []);
}

// Utility functions
function loadClusters() {
    try {
        return fs.readJsonSync(CLUSTERS_FILE);
    } catch (error) {
        logger.error('Error loading clusters:', error);
        return [];
    }
}

function saveClusters(clusters) {
    try {
        fs.writeJsonSync(CLUSTERS_FILE, clusters, { spaces: 2 });
        return true;
    } catch (error) {
        logger.error('Error saving clusters:', error);
        return false;
    }
}

// Notes utility functions
function loadNotes() {
    try {
        return fs.readJsonSync(NOTES_FILE);
    } catch (error) {
        logger.error('Error loading notes:', error);
        return [];
    }
}

function saveNotes(notes) {
    try {
        fs.writeJsonSync(NOTES_FILE, notes, { spaces: 2 });
        return true;
    } catch (error) {
        logger.error('Error saving notes:', error);
        return false;
    }
}

function cleanExpiredNotes() {
    const notes = loadNotes();
    const now = new Date();
    
    // Remove notes that are expired OR have been accessed
    const validNotes = notes.filter(note => {
        const isExpired = new Date(note.expiresAt) <= now;
        const wasAccessed = note.accessed === true;
        return !isExpired && !wasAccessed;
    });
    
    if (validNotes.length !== notes.length) {
        saveNotes(validNotes);
        logger.info(`Cleaned ${notes.length - validNotes.length} notes (expired or accessed)`);
    }
    
    return validNotes;
}

function checkKubesealInstalled() {
    try {
        execSync('which kubeseal', { stdio: 'pipe' });
        return true;
    } catch (error) {
        return false;
    }
}

async function executeKubeseal(secretYaml, certPath, namespace = 'default') {
    return new Promise((resolve, reject) => {
        const tmpSecretFile = path.join(__dirname, 'tmp', `secret-${uuidv4()}.yaml`);
        
        try {
            // Write secret to temporary file
            fs.writeFileSync(tmpSecretFile, secretYaml);
            
            // Execute kubeseal command
            const kubesealProcess = spawn('kubeseal', [
                '--cert', certPath,
                '--format', 'yaml',
                '--namespace', namespace
            ], {
                stdio: ['pipe', 'pipe', 'pipe']
            });
            
            let output = '';
            let error = '';
            
            // Pipe the secret file to kubeseal
            const secretStream = fs.createReadStream(tmpSecretFile);
            secretStream.pipe(kubesealProcess.stdin);
            
            kubesealProcess.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            kubesealProcess.stderr.on('data', (data) => {
                error += data.toString();
            });
            
            kubesealProcess.on('close', (code) => {
                // Clean up temporary file
                fs.removeSync(tmpSecretFile);
                
                if (code === 0) {
                    resolve(output);
                } else {
                    reject(new Error(`Kubeseal failed: ${error}`));
                }
            });
            
            kubesealProcess.on('error', (err) => {
                fs.removeSync(tmpSecretFile);
                reject(new Error(`Failed to start kubeseal: ${err.message}`));
            });
            
        } catch (err) {
            if (fs.existsSync(tmpSecretFile)) {
                fs.removeSync(tmpSecretFile);
            }
            reject(err);
        }
    });
}

// Routes

// Health check
app.get('/api/health', (req, res) => {
    const kubesealInstalled = checkKubesealInstalled();
    res.json({
        status: 'ok',
        kubeseal: kubesealInstalled ? 'installed' : 'not installed',
        timestamp: new Date().toISOString()
    });
});

// Get all clusters
app.get('/api/clusters', (req, res) => {
    try {
        const clusters = loadClusters();
        // Don't send certificate content in list view
        const clustersWithoutCerts = clusters.map(cluster => ({
            id: cluster.id,
            name: cluster.name,
            hasCertificate: !!cluster.certPath
        }));
        const sanitizedResponse = xssProtection.createSafeResponse(clustersWithoutCerts);
        res.json(sanitizedResponse);
    } catch (error) {
        logger.error('Error getting clusters:', error);
        res.status(500).json({ error: 'Failed to load clusters' });
    }
});

// Get specific cluster
app.get('/api/clusters/:id', (req, res) => {
    try {
        const clusters = loadClusters();
        const cluster = clusters.find(c => c.id === req.params.id);
        
        if (!cluster) {
            return res.status(404).json({ error: 'Cluster not found' });
        }
        
        // Don't send certificate content
        res.json({
            id: cluster.id,
            name: cluster.name,
            hasCertificate: !!cluster.certPath
        });
    } catch (error) {
        logger.error('Error getting cluster:', error);
        res.status(500).json({ error: 'Failed to load cluster' });
    }
});

// Add new cluster
app.post('/api/clusters', upload.single('certificate'), async (req, res) => {
    try {
        const { name, certContent } = req.body;
        const certificateFile = req.file;

        // Validate cluster name
        const nameValidation = validationUtils.validateClusterName(name);
        if (!nameValidation.isValid) {
            // Clean up uploaded file
            if (certificateFile) {
                fs.removeSync(certificateFile.path);
            }
            return res.status(400).json({ error: nameValidation.error });
        }

        if (!certificateFile && !certContent) {
            return res.status(400).json({ error: 'Certificate file or content is required' });
        }

        const clusters = loadClusters();

        // Check if cluster name already exists
        if (clusters.find(c => c.name === nameValidation.sanitizedName)) {
            // Clean up uploaded file
            if (certificateFile) {
                fs.removeSync(certificateFile.path);
            }
            return res.status(400).json({ error: 'Cluster with this name already exists' });
        }

        const clusterId = uuidv4();
        const certFileName = `${clusterId}.pem`;
        const certPath = path.join(__dirname, 'certs', certFileName);

        // Handle certificate input - either file or content string
        if (certificateFile) {
            // Enhanced file validation
            const fileValidation = await fileValidationUtils.validateUploadedFile(certificateFile);

            if (!fileValidation.isValid) {
                fs.removeSync(certificateFile.path);
                return res.status(400).json({
                    error: fileValidation.error,
                    details: fileValidation.details
                });
            }

            // Read and validate certificate file content
            const fileContent = fs.readFileSync(certificateFile.path, 'utf8');
            const certValidation = validationUtils.validatePemCertificate(fileContent);

            if (!certValidation.isValid) {
                fs.removeSync(certificateFile.path);
                return res.status(400).json({ error: certValidation.error });
            }

            // Write validated content to permanent location
            fs.writeFileSync(certPath, certValidation.sanitizedContent);
            fs.removeSync(certificateFile.path);

            logger.info(`Certificate file uploaded and validated for cluster '${nameValidation.sanitizedName}'`, {
                fileSize: certificateFile.size,
                pemType: fileValidation.details.pemType,
                detectedMime: fileValidation.details.detectedMime
            });
        } else if (certContent) {
            // Validate PEM certificate content
            const certValidation = validationUtils.validatePemCertificate(certContent);

            if (!certValidation.isValid) {
                return res.status(400).json({ error: certValidation.error });
            }

            // Write certificate content to file
            fs.writeFileSync(certPath, certValidation.sanitizedContent);
        }
        
        const newCluster = {
            id: clusterId,
            name: nameValidation.sanitizedName,
            certPath,
            createdAt: new Date().toISOString()
        };
        
        clusters.push(newCluster);
        
        if (!saveClusters(clusters)) {
            // Clean up certificate file if saving fails
            fs.removeSync(certPath);
            return res.status(500).json({ error: 'Failed to save cluster' });
        }
        
        logger.info(`Cluster added: ${name} (method: ${certificateFile ? 'file' : 'content'})`);
        res.status(201).json({
            id: newCluster.id,
            name: newCluster.name,
            hasCertificate: true
        });
        
    } catch (error) {
        logger.error('Error adding cluster:', error);
        // Clean up uploaded file if it exists
        if (req.file) {
            fs.removeSync(req.file.path);
        }
        res.status(500).json({ error: 'Failed to add cluster' });
    }
});

// Update cluster
app.put('/api/clusters/:id', upload.single('certificate'), (req, res) => {
    try {
        const { name, certContent } = req.body;
        const certificateFile = req.file;
        const clusterId = req.params.id;
        
        const clusters = loadClusters();
        const clusterIndex = clusters.findIndex(c => c.id === clusterId);
        
        if (clusterIndex === -1) {
            if (certificateFile) {
                fs.removeSync(certificateFile.path);
            }
            return res.status(404).json({ error: 'Cluster not found' });
        }
        
        const cluster = clusters[clusterIndex];
        
        // Check if new name conflicts with existing clusters (excluding current one)
        if (name && name !== cluster.name) {
            if (clusters.find(c => c.name === name && c.id !== clusterId)) {
                if (certificateFile) {
                    fs.removeSync(certificateFile.path);
                }
                return res.status(400).json({ error: 'Cluster with this name already exists' });
            }
            cluster.name = name;
        }
        
        // Update certificate if provided (either file or content)
        if (certificateFile || certContent) {
            // Remove old certificate
            if (cluster.certPath && fs.existsSync(cluster.certPath)) {
                fs.removeSync(cluster.certPath);
            }
            
            const certFileName = `${clusterId}.pem`;
            const certPath = path.join(__dirname, 'certs', certFileName);
            
            if (certificateFile) {
                // Move uploaded file to permanent location
                fs.moveSync(certificateFile.path, certPath);
            } else if (certContent) {
                // Validate PEM format
                if (!certContent.includes('-----BEGIN') || !certContent.includes('-----END')) {
                    return res.status(400).json({ error: 'Invalid PEM certificate format' });
                }
                
                // Write certificate content to file
                fs.writeFileSync(certPath, certContent.trim() + '\n');
            }
            
            cluster.certPath = certPath;
        }
        
        cluster.updatedAt = new Date().toISOString();
        clusters[clusterIndex] = cluster;
        
        if (!saveClusters(clusters)) {
            return res.status(500).json({ error: 'Failed to update cluster' });
        }
        
        logger.info(`Cluster updated: ${cluster.name} (method: ${certificateFile ? 'file' : certContent ? 'content' : 'name-only'})`);
        res.json({
            id: cluster.id,
            name: cluster.name,
            hasCertificate: !!cluster.certPath
        });
        
    } catch (error) {
        logger.error('Error updating cluster:', error);
        if (req.file) {
            fs.removeSync(req.file.path);
        }
        res.status(500).json({ error: 'Failed to update cluster' });
    }
});

// Delete cluster
app.delete('/api/clusters/:id', (req, res) => {
    try {
        const clusterId = req.params.id;
        const clusters = loadClusters();
        const clusterIndex = clusters.findIndex(c => c.id === clusterId);
        
        if (clusterIndex === -1) {
            return res.status(404).json({ error: 'Cluster not found' });
        }
        
        const cluster = clusters[clusterIndex];
        
        // Remove certificate file
        if (cluster.certPath && fs.existsSync(cluster.certPath)) {
            fs.removeSync(cluster.certPath);
        }
        
        // Remove cluster from array
        clusters.splice(clusterIndex, 1);
        
        if (!saveClusters(clusters)) {
            return res.status(500).json({ error: 'Failed to delete cluster' });
        }
        
        logger.info(`Cluster deleted: ${cluster.name}`);
        res.json({ message: 'Cluster deleted successfully' });
        
    } catch (error) {
        logger.error('Error deleting cluster:', error);
        res.status(500).json({ error: 'Failed to delete cluster' });
    }
});

// Convert secret to sealed secret
app.post('/api/convert', strictLimiter, async (req, res) => {
    try {
        let { secretYaml, clusterId, targetNamespace } = req.body;
        
        // Debug logging
        logger.info(`Convert request - clusterId: ${clusterId}, targetNamespace: '${targetNamespace}'`);
        
        if (!secretYaml || !clusterId) {
            return res.status(400).json({ error: 'Secret YAML and cluster ID are required' });
        }

        // Enhanced YAML validation
        const yamlValidation = validationUtils.validateSecret(secretYaml);
        if (!yamlValidation.isValid) {
            return res.status(400).json({ error: yamlValidation.error });
        }

        // Check if kubeseal is installed
        if (!checkKubesealInstalled()) {
            return res.status(500).json({ error: 'Kubeseal is not installed on the server' });
        }

        const clusters = loadClusters();
        const cluster = clusters.find(c => c.id === clusterId);

        if (!cluster) {
            return res.status(404).json({ error: 'Cluster not found' });
        }

        if (!cluster.certPath || !fs.existsSync(cluster.certPath)) {
            return res.status(400).json({ error: 'Certificate not found for this cluster' });
        }

        // Use validated and sanitized YAML
        const parsedSecret = yamlValidation.parsedObject;
        const sanitizedSecretYaml = yamlValidation.sanitizedYaml;
        
        // Extract namespace from secret, use targetNamespace, or default to 'default'
        let namespace = parsedSecret.metadata?.namespace || targetNamespace || 'default';
        
        // Debug logging for namespace resolution
        logger.info(`Namespace resolution - from secret: '${parsedSecret.metadata?.namespace}', targetNamespace: '${targetNamespace}', final: '${namespace}'`);
        
        // If no namespace in metadata, add it to the secret YAML
        let finalSecretYaml = sanitizedSecretYaml;
        if (!parsedSecret.metadata.namespace) {
            parsedSecret.metadata.namespace = namespace;
            // Update the YAML string with the namespace
            finalSecretYaml = yaml.dump(parsedSecret, {
                indent: 2,
                lineWidth: -1,
                noRefs: true
            });

            logger.info(`Added namespace '${namespace}' to secret YAML`);
        }

        // Convert to sealed secret
        let sealedSecretYaml = await executeKubeseal(finalSecretYaml, cluster.certPath, namespace);
        
        // Clean up the sealed secret YAML by removing unnecessary fields
        try {
            const parsedSealedSecret = yaml.load(sealedSecretYaml);
            
            // Remove creationTimestamp from metadata
            if (parsedSealedSecret.metadata && parsedSealedSecret.metadata.creationTimestamp) {
                delete parsedSealedSecret.metadata.creationTimestamp;
            }
            
            // Remove creationTimestamp from template metadata if it exists
            if (parsedSealedSecret.spec && parsedSealedSecret.spec.template && 
                parsedSealedSecret.spec.template.metadata && 
                parsedSealedSecret.spec.template.metadata.creationTimestamp) {
                delete parsedSealedSecret.spec.template.metadata.creationTimestamp;
            }
            
            // Regenerate clean YAML
            sealedSecretYaml = yaml.dump(parsedSealedSecret, {
                indent: 2,
                lineWidth: -1,
                noRefs: true
            });
            
            logger.info(`Cleaned sealed secret YAML (removed creationTimestamp)`);
        } catch (cleanupError) {
            logger.warn(`Failed to clean up sealed secret YAML: ${cleanupError.message}`);
            // Continue with original YAML if cleanup fails
        }
        
        logger.info(`Secret converted for cluster: ${cluster.name}`);
        res.json({
            sealedSecret: sealedSecretYaml,
            clusterName: cluster.name,
            namespace
        });
        
    } catch (error) {
        logger.error('Error converting secret:', error);
        res.status(500).json({ error: `Conversion failed: ${error.message}` });
    }
});

// Notes API endpoints

// Create new note
app.post('/api/notes', strictLimiter, (req, res) => {
    try {
        const { title, content } = req.body;

        if (!content || typeof content !== 'string' || !content.trim()) {
            return res.status(400).json({ error: 'Note content is required and must be a non-empty string' });
        }

        // Validate content length (max 50KB)
        if (content.length > 51200) {
            return res.status(400).json({ error: 'Note content is too large (max 50KB allowed)' });
        }

        // Validate title if provided
        if (title && (typeof title !== 'string' || title.length > 200)) {
            return res.status(400).json({ error: 'Note title must be a string with max 200 characters' });
        }

        // Clean expired notes before creating new one
        cleanExpiredNotes();

        const noteId = uuidv4();
        const now = new Date();
        const expiresAt = new Date(now.getTime() + (24 * 60 * 60 * 1000)); // 24 hours from now

        // Enhanced XSS protection and sanitization
        const sanitizedTitle = title ? xssProtection.sanitizeText(title.trim(), { maxLength: 200 }) : '';
        const sanitizedContent = xssProtection.sanitizeText(content.trim(), { maxLength: 51200 });

        // Additional XSS detection
        const titleXSSCheck = xssProtection.detectXSS(title || '');
        const contentXSSCheck = xssProtection.detectXSS(content);

        if (titleXSSCheck.hasXSS || contentXSSCheck.hasXSS) {
            logger.warn('XSS attempt detected in note creation', {
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                titlePatterns: titleXSSCheck.patterns,
                contentPatterns: contentXSSCheck.patterns
            });
            return res.status(400).json({ error: 'Content contains potentially malicious code' });
        }

        const newNote = {
            id: noteId,
            title: sanitizedTitle,
            content: sanitizedContent,
            createdAt: now.toISOString(),
            expiresAt: expiresAt.toISOString(),
            accessed: false
        };
        
        const notes = loadNotes();
        notes.push(newNote);
        
        if (!saveNotes(notes)) {
            return res.status(500).json({ error: 'Failed to save note' });
        }
        
        logger.info(`Note created with ID: ${noteId}, expires at: ${expiresAt.toISOString()}`);
        
        const response = xssProtection.createSafeResponse({
            id: noteId,
            link: `${req.protocol}://${req.get('host')}/note/${noteId}`,
            expiresAt: expiresAt.toISOString()
        });
        res.status(201).json(response);
        
    } catch (error) {
        logger.error('Error creating note:', error);
        res.status(500).json({ error: 'Failed to create note' });
    }
});

// Get note by ID
app.get('/api/notes/:id', (req, res) => {
    try {
        const noteId = req.params.id;
        
        // Clean expired notes first
        const validNotes = cleanExpiredNotes();
        const note = validNotes.find(n => n.id === noteId);
        
        if (!note) {
            return res.status(404).json({ error: 'Note not found or has expired' });
        }
        
        // Mark note as accessed and save
        note.accessed = true;
        note.accessedAt = new Date().toISOString();
        saveNotes(validNotes);
        
        logger.info(`Note accessed: ${noteId}, will be deleted on next cleanup`);
        
        res.json({
            id: note.id,
            title: note.title,
            content: note.content,
            createdAt: note.createdAt,
            expiresAt: note.expiresAt
        });
        
    } catch (error) {
        logger.error('Error retrieving note:', error);
        res.status(500).json({ error: 'Failed to retrieve note' });
    }
});

// Serve note view page
app.get('/note/:id', (req, res) => {
    // This will serve a simple HTML page for viewing notes
    const noteViewHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Note - Sealify</title>
    <link rel="stylesheet" href="/styles.css">
    <style>
        .note-viewer {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .note-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-top: 1rem;
        }
        .note-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
        }
        .note-text {
            white-space: pre-wrap;
            font-family: 'Monaco', 'Consolas', monospace;
            line-height: 1.6;
            color: #374151;
        }
        .note-meta {
            font-size: 0.875rem;
            color: #64748b;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e2e8f0;
        }
        .error-message {
            background: #fee2e2;
            color: #dc2626;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        .loading {
            text-align: center;
            color: #64748b;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <h1>🔐 Sealify</h1>
            <p class="header-subtitle">Secure Note</p>
        </div>
    </header>
    
    <div class="note-viewer">
        <div id="loading" class="loading">
            <p>Loading secure note...</p>
        </div>
        <div id="noteContent" class="note-content" style="display: none;"></div>
        <div id="errorMessage" class="error-message" style="display: none;"></div>
    </div>

    <script>
        const noteId = window.location.pathname.split('/').pop();
        
        async function loadNote() {
            try {
                const response = await fetch(\`/api/notes/\${noteId}\`);
                const loading = document.getElementById('loading');
                const noteContent = document.getElementById('noteContent');
                const errorMessage = document.getElementById('errorMessage');
                
                loading.style.display = 'none';
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to load note');
                }
                
                const note = await response.json();
                
                const createdAt = new Date(note.createdAt).toLocaleString();
                const expiresAt = new Date(note.expiresAt).toLocaleString();
                
                // Note: Content is already escaped on the server side for additional security
                noteContent.innerHTML = \`
                    \${note.title ? \`<div class="note-title">\${escapeHtml(note.title)}</div>\` : ''}
                    <div class="note-text">\${escapeHtml(note.content)}</div>
                    <div class="note-meta">
                        <div>📅 Created: \${escapeHtml(createdAt)}</div>
                        <div>⏰ Expires: \${escapeHtml(expiresAt)}</div>
                        <div>🔒 This note has been accessed and will be automatically deleted</div>
                    </div>
                \`;
                noteContent.style.display = 'block';
                
            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.textContent = error.message;
                errorDiv.style.display = 'block';
            }
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        loadNote();
    </script>
</body>
</html>`;
    
    res.send(noteViewHtml);
});

// Base64 encode/decode utilities
app.post('/api/base64/encode', (req, res) => {
    try {
        const { text } = req.body;
        if (!text) {
            return res.status(400).json({ error: 'Text is required' });
        }
        
        const encoded = Buffer.from(text, 'utf8').toString('base64');
        res.json({ encoded });
    } catch (error) {
        logger.error('Error encoding base64:', error);
        res.status(500).json({ error: 'Failed to encode' });
    }
});

app.post('/api/base64/decode', (req, res) => {
    try {
        const { encoded } = req.body;
        if (!encoded) {
            return res.status(400).json({ error: 'Encoded text is required' });
        }
        
        const decoded = Buffer.from(encoded, 'base64').toString('utf8');
        res.json({ decoded });
    } catch (error) {
        logger.error('Error decoding base64:', error);
        res.status(500).json({ error: 'Failed to decode - invalid base64' });
    }
});

// Secret transformation utilities
app.post('/api/secret/data-to-stringdata', strictLimiter, (req, res) => {
    try {
        const { secretYaml } = req.body;
        if (!secretYaml) {
            return res.status(400).json({ error: 'Secret YAML is required' });
        }

        // Enhanced YAML validation
        const yamlValidation = validationUtils.validateSecret(secretYaml);
        if (!yamlValidation.isValid) {
            return res.status(400).json({ error: yamlValidation.error });
        }

        const parsedSecret = yamlValidation.parsedObject;
        
        // Convert data to stringData
        if (parsedSecret.data) {
            if (!parsedSecret.stringData) {
                parsedSecret.stringData = {};
            }
            
            // Decode each base64 value in data and move to stringData
            for (const [key, base64Value] of Object.entries(parsedSecret.data)) {
                try {
                    const decodedValue = Buffer.from(base64Value, 'base64').toString('utf8');
                    parsedSecret.stringData[key] = decodedValue;
                } catch (decodeError) {
                    logger.warn(`Failed to decode data.${key}: ${decodeError.message}`);
                    // Keep the original base64 value if decode fails
                    parsedSecret.stringData[key] = base64Value;
                }
            }
            
            // Remove the data field
            delete parsedSecret.data;
        }
        
        // Generate the transformed YAML
        const transformedYaml = yaml.dump(parsedSecret, {
            indent: 2,
            lineWidth: -1,
            noRefs: true
        });
        
        logger.info('Secret transformed from data to stringData');
        res.json({
            transformedSecret: transformedYaml,
            message: 'Successfully converted data (base64) to stringData (plain text)'
        });
        
    } catch (error) {
        logger.error('Error transforming secret data to stringData:', error);
        res.status(500).json({ error: `Transformation failed: ${error.message}` });
    }
});

app.post('/api/secret/stringdata-to-data', strictLimiter, (req, res) => {
    try {
        const { secretYaml } = req.body;
        if (!secretYaml) {
            return res.status(400).json({ error: 'Secret YAML is required' });
        }

        // Enhanced YAML validation
        const yamlValidation = validationUtils.validateSecret(secretYaml);
        if (!yamlValidation.isValid) {
            return res.status(400).json({ error: yamlValidation.error });
        }

        const parsedSecret = yamlValidation.parsedObject;
        
        // Convert stringData to data
        if (parsedSecret.stringData) {
            if (!parsedSecret.data) {
                parsedSecret.data = {};
            }
            
            // Encode each string value in stringData and move to data
            for (const [key, stringValue] of Object.entries(parsedSecret.stringData)) {
                const base64Value = Buffer.from(stringValue.toString(), 'utf8').toString('base64');
                parsedSecret.data[key] = base64Value;
            }
            
            // Remove the stringData field
            delete parsedSecret.stringData;
        }
        
        // Generate the transformed YAML
        const transformedYaml = yaml.dump(parsedSecret, {
            indent: 2,
            lineWidth: -1,
            noRefs: true
        });
        
        logger.info('Secret transformed from stringData to data');
        res.json({
            transformedSecret: transformedYaml,
            message: 'Successfully converted stringData (plain text) to data (base64)'
        });
        
    } catch (error) {
        logger.error('Error transforming secret stringData to data:', error);
        res.status(500).json({ error: `Transformation failed: ${error.message}` });
    }
});

// Serve static files (frontend)
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Enhanced error handling middleware
app.use((error, req, res, next) => {
    logger.error('Unhandled error:', {
        message: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });

    // Handle multer errors
    if (error instanceof multer.MulterError) {
        switch (error.code) {
            case 'LIMIT_FILE_SIZE':
                return res.status(400).json({
                    error: 'File too large',
                    details: { maxSize: '5MB' }
                });
            case 'LIMIT_FILE_COUNT':
                return res.status(400).json({
                    error: 'Too many files',
                    details: { maxFiles: 1 }
                });
            case 'LIMIT_FIELD_COUNT':
                return res.status(400).json({
                    error: 'Too many fields',
                    details: { maxFields: 1 }
                });
            case 'LIMIT_PART_COUNT':
                return res.status(400).json({
                    error: 'Too many parts in multipart data',
                    details: { maxParts: 2 }
                });
            case 'LIMIT_UNEXPECTED_FILE':
                return res.status(400).json({
                    error: 'Unexpected file field',
                    details: { expectedField: 'certificate' }
                });
            default:
                return res.status(400).json({
                    error: 'File upload error',
                    details: { code: error.code }
                });
        }
    }

    // Handle validation errors
    if (error.message && error.message.includes('not allowed')) {
        return res.status(400).json({
            error: error.message,
            details: { type: 'validation_error' }
        });
    }

    // Generic error response (don't expose internal details)
    res.status(500).json({
        error: 'Internal server error',
        details: { timestamp: new Date().toISOString() }
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'Not found' });
});

// Start server
app.listen(PORT, () => {
    logger.info(`Sealify server running on port ${PORT}`);
    logger.info(`Kubeseal ${checkKubesealInstalled() ? 'is' : 'is not'} installed`);
    console.log(`\n🔐 Sealify`);
    console.log(`📡 Server: http://localhost:${PORT}`);
    console.log(`🔐 Kubeseal: ${checkKubesealInstalled() ? '✅ Installed' : '❌ Not installed'}`);
    console.log(`📝 Logs: ./logs/`);
    
    // Set up automatic note cleanup every 5 minutes
    setInterval(() => {
        cleanExpiredNotes();
    }, 5 * 60 * 1000); // 5 minutes
    
    // Initial cleanup on startup
    cleanExpiredNotes();
    logger.info('Automatic note cleanup initialized (runs every 5 minutes)');
});

module.exports = app;