const fs = require('fs-extra');
const path = require('path');

// Test the utilities directly
const validationUtils = require('../utils/validation');
const fileValidationUtils = require('../utils/fileValidation');
const xssProtection = require('../utils/xssProtection');

// Simple test framework
function test(name, fn) {
    try {
        fn();
        console.log(`✓ ${name}`);
        return true;
    } catch (error) {
        console.log(`✗ ${name}: ${error.message}`);
        return false;
    }
}

function expect(actual) {
    return {
        toBe: (expected) => {
            if (actual !== expected) {
                throw new Error(`Expected ${expected}, got ${actual}`);
            }
        },
        toContain: (expected) => {
            if (!actual.includes(expected)) {
                throw new Error(`Expected "${actual}" to contain "${expected}"`);
            }
        },
        not: {
            toContain: (expected) => {
                if (actual.includes(expected)) {
                    throw new Error(`Expected "${actual}" not to contain "${expected}"`);
                }
            }
        },
        toMatch: (pattern) => {
            if (!pattern.test(actual)) {
                throw new Error(`Expected "${actual}" to match pattern ${pattern}`);
            }
        },
        toBeGreaterThan: (expected) => {
            if (actual <= expected) {
                throw new Error(`Expected ${actual} to be greater than ${expected}`);
            }
        },
        toBeDefined: () => {
            if (actual === undefined) {
                throw new Error('Expected value to be defined');
            }
        }
    };
}

// Security Enhancements Tests
console.log('🔒 Running Security Enhancement Tests...\n');

// Input Validation and YAML Sanitization Tests
console.log('📝 Input Validation and YAML Sanitization:');

test('should validate safe YAML content', () => {
            const safeYaml = `
apiVersion: v1
kind: Secret
metadata:
  name: test-secret
data:
  username: dGVzdA==
`;
            const result = validationUtils.validateYaml(safeYaml);
            expect(result.isValid).toBe(true);
});

test('should reject YAML with excessive anchors (YAML bomb)', () => {
    const yamlBomb = `
a: &anchor
  - data
  - *anchor
  - *anchor
`;
    const result = validationUtils.validateYaml(yamlBomb);
    expect(result.isValid).toBe(false);
    expect(result.error).toContain('excessive anchor');
});

test('should validate Kubernetes Secret schema', () => {
    const validSecret = {
        apiVersion: 'v1',
        kind: 'Secret',
        metadata: { name: 'test-secret' },
        data: { username: 'dGVzdA==' }
    };
    const result = validationUtils.validateSecret(validSecret);
    expect(result.isValid).toBe(true);
});

test('should reject invalid Secret schema', () => {
    const invalidSecret = {
        apiVersion: 'v1',
        kind: 'ConfigMap', // Wrong kind
        metadata: { name: 'test-secret' }
    };
    const result = validationUtils.validateSecret(invalidSecret);
    expect(result.isValid).toBe(false);
});

test('should validate PEM certificate format', () => {
    const validPem = `-----BEGIN CERTIFICATE-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7Q==
-----END CERTIFICATE-----`;
    const result = validationUtils.validatePemCertificate(validPem);
    expect(result.isValid).toBe(true);
});

test('should sanitize HTML content', () => {
    const maliciousHtml = '<script>alert("xss")</script><p>Safe content</p>';
    const sanitized = validationUtils.sanitizeHtml(maliciousHtml);
    expect(sanitized).not.toContain('<script>');
    expect(sanitized).toContain('Safe content');
});

console.log('\n📁 File Upload Security:');

test('should detect suspicious content patterns', () => {
    const suspiciousContent = '<script>alert("xss")</script>';
    const result = fileValidationUtils.checkSuspiciousContent(suspiciousContent);
    expect(result.isValid).toBe(false);
});

test('should validate PEM format in files', () => {
    const invalidPem = 'This is not a PEM file';
    const result = fileValidationUtils.validatePemFormat(invalidPem);
    expect(result.isValid).toBe(false);
});

test('should generate secure filenames', () => {
    const originalName = '../../../etc/passwd';
    const secureName = fileValidationUtils.generateSecureFilename(originalName);
    expect(secureName).not.toContain('../');
    expect(secureName).toMatch(/^[a-zA-Z0-9_\-]+_\d+_[a-z0-9]+$/);
});

console.log('\n🛡️ XSS Prevention:');

test('should sanitize text input', () => {
    const maliciousInput = '<script>alert("xss")</script>Hello World';
    const sanitized = xssProtection.sanitizeText(maliciousInput);
    expect(sanitized).not.toContain('<script>');
    expect(sanitized).toContain('Hello World');
});

test('should sanitize HTML content aggressively', () => {
    const maliciousHtml = '<iframe src="javascript:alert(1)"></iframe><p>Safe</p>';
    const sanitized = xssProtection.sanitizeHtml(maliciousHtml);
    expect(sanitized).not.toContain('<iframe>');
    expect(sanitized).not.toContain('javascript:');
});

test('should sanitize URLs', () => {
    const maliciousUrl = 'javascript:alert("xss")';
    const sanitized = xssProtection.sanitizeUrl(maliciousUrl);
    expect(sanitized).toBe('');
});

test('should sanitize JSON data recursively', () => {
    const maliciousData = {
        name: '<script>alert("xss")</script>',
        nested: {
            content: 'javascript:alert(1)'
        },
        array: ['<iframe>', 'safe content']
    };
    const sanitized = xssProtection.sanitizeJsonData(maliciousData);
    expect(sanitized.name).not.toContain('<script>');
    expect(sanitized.nested.content).not.toContain('javascript:');
    expect(sanitized.array[0]).not.toContain('<iframe>');
});

test('should validate and sanitize cluster names', () => {
    const maliciousName = '<script>alert("xss")</script>MyCluster';
    const result = xssProtection.sanitizeClusterName(maliciousName);
    expect(result.isValid).toBe(true);
    expect(result.sanitized).not.toContain('<script>');
    expect(result.sanitized).toMatch(/^[a-z0-9\-\.]+$/);
});

test('should detect XSS patterns', () => {
    const xssContent = '<script>alert("xss")</script>';
    const result = xssProtection.detectXSS(xssContent);
    expect(result.hasXSS).toBe(true);
    expect(result.patterns.length).toBeGreaterThan(0);
});

test('should create safe response objects', () => {
    const unsafeData = {
        message: '<script>alert("xss")</script>',
        user: 'javascript:alert(1)'
    };
    const safeResponse = xssProtection.createSafeResponse(unsafeData);
    expect(safeResponse._sanitized).toBe(true);
    expect(safeResponse.message).not.toContain('<script>');
});

console.log('\n⚠️ Error Handling:');

test('should handle validation errors gracefully', () => {
    const result = validationUtils.validateYaml('invalid: yaml: content: [');
    expect(result.isValid).toBe(false);
    expect(result.error).toBeDefined();
});

// Helper function to run all tests
if (require.main === module) {
    console.log('Running security tests...');
    
    // Simple test runner
    const runTests = async () => {
        try {
            console.log('✓ Testing YAML validation...');
            const yamlResult = validationUtils.validateYaml('apiVersion: v1\nkind: Secret');
            console.log('  YAML validation:', yamlResult.isValid ? 'PASS' : 'FAIL');
            
            console.log('✓ Testing XSS protection...');
            const xssResult = xssProtection.sanitizeText('<script>alert("test")</script>');
            console.log('  XSS protection:', !xssResult.includes('<script>') ? 'PASS' : 'FAIL');
            
            console.log('✓ Testing file validation...');
            const fileResult = fileValidationUtils.generateSecureFilename('../../../etc/passwd');
            console.log('  File validation:', !fileResult.includes('../') ? 'PASS' : 'FAIL');
            
            console.log('\n🔒 All security tests completed!');
            
        } catch (error) {
            console.error('❌ Test error:', error.message);
        }
    };
    
    runTests();
}

module.exports = {
    validationUtils,
    fileValidationUtils,
    xssProtection
};
