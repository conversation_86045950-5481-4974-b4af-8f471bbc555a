<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sealify - Kubernetes Sealed Secrets Manager</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <div class="header-content">
            <h1>🔐 Sealify</h1>
            <p class="header-subtitle">Kubernetes Sealed Secrets Manager</p>
        </div>

        <nav role="navigation" aria-label="Main navigation">
            <button id="userPageBtn" class="nav-btn active" aria-pressed="true">
                <span class="nav-icon" aria-hidden="true">🔒</span>
                Sealed Secrets
            </button>
            <button id="transformPageBtn" class="nav-btn" aria-pressed="false">
                <span class="nav-icon" aria-hidden="true">🔄</span>
                Secret Transform
            </button>
            <button id="base64PageBtn" class="nav-btn" aria-pressed="false">
                <span class="nav-icon" aria-hidden="true">🔧</span>
                Base64 Tools
            </button>
            <button id="randomPageBtn" class="nav-btn" aria-pressed="false">
                <span class="nav-icon" aria-hidden="true">⚡</span>
                Generator
            </button>
            <button id="notesPageBtn" class="nav-btn" aria-pressed="false">
                <span class="nav-icon" aria-hidden="true">📝</span>
                Secure Notes
            </button>
            <button id="adminPageBtn" class="nav-btn" aria-pressed="false">
                <span class="nav-icon" aria-hidden="true">⚙️</span>
                Settings
            </button>
        </nav>
    </header>

    <main>
        <!-- User Page -->
        <div id="userPage" class="page active">
            <div class="page-header">
                <h2>Create Sealed Secret</h2>
                <p class="page-description">Convert your Kubernetes secrets into sealed secrets for secure deployment</p>
            </div>
            <div class="form-container">
                <div class="input-group">
                    <label for="secretInput">Secret YAML:</label>
                    <div class="textarea-with-button">
                        <textarea id="secretInput" placeholder="Enter your Kubernetes secret YAML here..."></textarea>
                        <button type="button" id="insertSampleBtn" class="sample-btn">📋 Sample</button>
                    </div>
                    <small>💡 Tip: If no namespace is specified, the target namespace below will be used</small>
                </div>
                
                <div class="input-group">
                    <label for="namespaceInput">Target Namespace (optional):</label>
                    <input type="text" id="namespaceInput" placeholder="default" value="default">
                    <small>💡 This will be used if the secret YAML doesn't specify a namespace</small>
                </div>
                
                <div class="input-group">
                    <label for="clusterSelect">Select Cluster:</label>
                    <select id="clusterSelect">
                        <option value="">Choose a cluster...</option>
                    </select>
                </div>
                
                <button id="convertBtn" class="primary-btn">🔒 Convert to Sealed Secret</button>
                
                <div class="input-group">
                    <label for="sealedSecretOutput">Sealed Secret YAML:</label>
                    <div class="textarea-with-button">
                        <textarea id="sealedSecretOutput" readonly placeholder="Your sealed secret will appear here..."></textarea>
                        <button id="copyBtn" class="copy-btn">📋 Copy</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secret Transform Page -->
        <div id="transformPage" class="page">
            <div class="page-header">
                <h2>🔄 Secret Format Transformation</h2>
                <p class="page-description">Transform between Kubernetes secret data formats (data ↔ stringData)</p>
            </div>
            <div class="form-container">
                <!-- Direction Toggle -->
                <div class="transform-direction-selector">
                    <div class="selector-header">
                        <h3>🔄 Transformation Direction</h3>
                        <p>Choose the conversion type for your Kubernetes secret</p>
                    </div>
                    <div class="selector-options">
                        <button id="dataToStringModeBtn" class="transform-option active" data-mode="dataToString">
                            <div class="option-icon">
                                <div class="icon-container decode">
                                    <span class="icon-symbol">🔓</span>
                                </div>
                            </div>
                            <div class="option-content">
                                <div class="option-title">data → stringData</div>
                                <div class="option-subtitle">Decode Base64 to Plain Text</div>
                                <div class="option-description">
                                    Convert base64-encoded secret values to readable plain text format
                                </div>
                                <div class="option-example">
                                    <code>dXNlcm5hbWU=</code> → <code>username</code>
                                </div>
                            </div>
                            <div class="option-indicator">
                                <div class="indicator-dot"></div>
                            </div>
                        </button>
                        
                        <button id="stringToDataModeBtn" class="transform-option" data-mode="stringToData">
                            <div class="option-icon">
                                <div class="icon-container encode">
                                    <span class="icon-symbol">🔒</span>
                                </div>
                            </div>
                            <div class="option-content">
                                <div class="option-title">stringData → data</div>
                                <div class="option-subtitle">Encode Plain Text to Base64</div>
                                <div class="option-description">
                                    Convert readable plain text values to base64-encoded format
                                </div>
                                <div class="option-example">
                                    <code>password</code> → <code>cGFzc3dvcmQ=</code>
                                </div>
                            </div>
                            <div class="option-indicator">
                                <div class="indicator-dot"></div>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- Input Section -->
                <div class="input-group">
                    <label for="transformInput" id="inputLabel">Secret with data (base64):</label>
                    <div class="textarea-with-button">
                        <textarea id="transformInput" placeholder="apiVersion: v1
kind: Secret
metadata:
  name: my-secret
type: Opaque
data:
  username: dXNlcm5hbWU=
  password: cGFzc3dvcmQ="></textarea>
                        <button type="button" id="insertSampleBtn" class="sample-btn">📋 Sample</button>
                    </div>
                </div>
                
                <!-- Transform Button -->
                <button id="transformBtn" class="primary-btn">🔓 Convert to stringData</button>
                
                <!-- Output Section -->
                <div class="input-group">
                    <label for="transformOutput" id="outputLabel">Result (stringData format):</label>
                    <div class="textarea-with-button">
                        <textarea id="transformOutput" readonly placeholder="Transformed secret will appear here..."></textarea>
                        <button id="copyTransformBtn" class="copy-btn">📋 Copy</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Base64 Tools Page -->
        <div id="base64Page" class="page">
            <div class="page-header">
                <h2>Base64 Encode/Decode</h2>
                <p class="page-description">Encode and decode Base64 strings for Kubernetes secret data</p>
            </div>
            <div class="form-container">
                <div class="base64-tools">
                    <div class="tool-section">
                        <h3>Encode to Base64</h3>
                        <div class="input-group">
                            <label for="textToEncode">Text to Encode:</label>
                            <textarea id="textToEncode" placeholder="Enter text to encode to base64..."></textarea>
                        </div>
                        <button id="encodeBtn" class="primary-btn">🔼 Encode</button>
                        <div class="input-group">
                            <label for="encodedOutput">Base64 Output:</label>
                            <div class="textarea-with-button">
                                <textarea id="encodedOutput" readonly placeholder="Encoded text will appear here..."></textarea>
                                <button id="copyEncodedBtn" class="copy-btn">📋 Copy</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tool-section">
                        <h3>Decode from Base64</h3>
                        <div class="input-group">
                            <label for="textToDecode">Base64 to Decode:</label>
                            <textarea id="textToDecode" placeholder="Enter base64 text to decode..."></textarea>
                        </div>
                        <button id="decodeBtn" class="primary-btn">🔽 Decode</button>
                        <div class="input-group">
                            <label for="decodedOutput">Decoded Output:</label>
                            <div class="textarea-with-button">
                                <textarea id="decodedOutput" readonly placeholder="Decoded text will appear here..."></textarea>
                                <button id="copyDecodedBtn" class="copy-btn">📋 Copy</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Generator Tools Page -->
        <div id="randomPage" class="page">
            <div class="page-header">
                <h2>Generator Tools</h2>
                <p class="page-description">Create secure passwords and unique identifiers for your projects</p>
            </div>
            <div class="form-container">
                <div class="generator-tools">
                <!-- Password Generator Card -->
                <div class="generator-card primary-card">
                    <div class="card-header">
                        <div class="card-icon">🔐</div>
                        <div class="card-title">
                            <h3>Password Generator</h3>
                            <p>Create strong, secure passwords</p>
                        </div>
                    </div>

                    <div class="card-content">
                        <div class="form-row">
                            <div class="form-field">
                                <div class="slider-header">
                                    <label for="passwordLength">Characters</label>
                                    <span class="slider-value" id="passwordLengthValue">16</span>
                                </div>
                                <input type="range" id="passwordLength" min="4" max="128" value="16" class="length-slider">
                            </div>
                            <div class="form-field">
                                <div class="slider-header">
                                    <label for="passwordCount">Count</label>
                                    <span class="slider-value" id="passwordCountValue">1</span>
                                </div>
                                <input type="range" id="passwordCount" min="1" max="10" value="1" class="length-slider">
                            </div>
                        </div>

                        <div class="form-section">
                            <label class="section-label">Character Types</label>
                            <div class="toggle-group">
                                <label class="toggle-option">
                                    <input type="checkbox" id="includeUppercase" checked>
                                    <span class="toggle-switch"></span>
                                    <span class="toggle-label">ABC</span>
                                </label>
                                <label class="toggle-option">
                                    <input type="checkbox" id="includeLowercase" checked>
                                    <span class="toggle-switch"></span>
                                    <span class="toggle-label">abc</span>
                                </label>
                                <label class="toggle-option">
                                    <input type="checkbox" id="includeNumbers" checked>
                                    <span class="toggle-switch"></span>
                                    <span class="toggle-label">123</span>
                                </label>
                                <label class="toggle-option">
                                    <input type="checkbox" id="includeSymbols" checked>
                                    <span class="toggle-switch"></span>
                                    <span class="toggle-label">!@#</span>
                                </label>
                                <label class="toggle-option">
                                    <input type="checkbox" id="excludeSimilar">
                                    <span class="toggle-switch"></span>
                                    <span class="toggle-label">No 0Ol1I</span>
                                </label>
                            </div>
                        </div>

                        <button id="generatePasswordBtn" class="generate-btn primary">
                            <span class="btn-icon">⚡</span>
                            Generate Password
                        </button>

                        <div class="output-section">
                            <div class="output-wrapper">
                                <textarea id="passwordOutput" readonly placeholder="Your generated passwords will appear here..." class="output-field"></textarea>
                                <button id="copyPasswordBtn" class="copy-btn">
                                    <span class="copy-icon">📋</span>
                                    Copy
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- UUID Generator Card -->
                <div class="generator-card secondary-card">
                    <div class="card-header">
                        <div class="card-icon">🆔</div>
                        <div class="card-title">
                            <h3>UUID Generator</h3>
                            <p>Generate unique identifiers</p>
                        </div>
                    </div>

                    <div class="card-content">
                        <div class="form-row">
                            <div class="form-field">
                                <label for="uuidVersion">Version</label>
                                <select id="uuidVersion" class="select-input">
                                    <option value="v4" selected>v4 (Random)</option>
                                    <option value="v1">v1 (Timestamp)</option>
                                </select>
                            </div>
                            <div class="form-field">
                                <div class="slider-header">
                                    <label for="uuidCount">Count</label>
                                    <span class="slider-value" id="uuidCountValue">1</span>
                                </div>
                                <input type="range" id="uuidCount" min="1" max="10" value="1" class="length-slider">
                            </div>
                        </div>

                        <div class="form-section">
                            <label class="section-label">Format Options</label>
                            <div class="toggle-group compact">
                                <label class="toggle-option">
                                    <input type="checkbox" id="uuidUppercase">
                                    <span class="toggle-switch"></span>
                                    <span class="toggle-label">Uppercase</span>
                                </label>
                                <label class="toggle-option">
                                    <input type="checkbox" id="uuidNoDashes">
                                    <span class="toggle-switch"></span>
                                    <span class="toggle-label">Remove dashes</span>
                                </label>
                            </div>
                        </div>

                        <button id="generateUuidBtn" class="generate-btn secondary">
                            <span class="btn-icon">⚡</span>
                            Generate UUID
                        </button>

                        <div class="output-section">
                            <div class="output-wrapper">
                                <textarea id="uuidOutput" readonly placeholder="Your generated UUIDs will appear here..." class="output-field"></textarea>
                                <button id="copyUuidBtn" class="copy-btn">
                                    <span class="copy-icon">📋</span>
                                    Copy
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Utilities Row -->
                <div class="utilities-row">
                    <div class="utility-card">
                        <div class="utility-header">
                            <div class="utility-icon">🔢</div>
                            <h4>Random Number</h4>
                        </div>
                        <div class="utility-content">
                            <div class="range-inputs">
                                <input type="number" id="randomMin" value="1" class="range-input" placeholder="Min">
                                <span class="range-divider">to</span>
                                <input type="number" id="randomMax" value="100" class="range-input" placeholder="Max">
                            </div>
                            <button id="generateNumberBtn" class="utility-btn">Generate</button>
                            <div class="utility-output">
                                <input type="text" id="numberOutput" readonly placeholder="Random number" class="output-input">
                                <button id="copyNumberBtn" class="copy-btn-small">
                                    <span class="copy-icon">📋</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="utility-card">
                        <div class="utility-header">
                            <div class="utility-icon">🔤</div>
                            <h4>Random String</h4>
                        </div>
                        <div class="utility-content">
                            <div class="string-controls">
                                <input type="number" id="stringLength" value="8" class="length-input" placeholder="Length">
                                <select id="stringType" class="type-select">
                                    <option value="alphanumeric">Alphanumeric</option>
                                    <option value="alphabetic">Letters only</option>
                                    <option value="numeric">Numbers only</option>
                                    <option value="hex">Hexadecimal</option>
                                </select>
                            </div>
                            <button id="generateStringBtn" class="utility-btn">Generate</button>
                            <div class="utility-output">
                                <input type="text" id="stringOutput" readonly placeholder="Random string" class="output-input">
                                <button id="copyStringBtn" class="copy-btn-small">
                                    <span class="copy-icon">📋</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                </div> <!-- Close .generator-tools -->
            </div>
        </div>

        <!-- Secure Notes Page -->
        <div id="notesPage" class="page">
            <div class="page-header">
                <h2>Send Notes Securely</h2>
                <p class="page-description">Create secure notes that auto-delete after 24 hours. Share sensitive information safely.</p>
            </div>
            <div class="form-container">
                <form id="createNoteForm">
                    <div class="input-group">
                        <label for="noteTitle">Title (Optional):</label>
                        <input type="text" id="noteTitle" placeholder="e.g., Database credentials, API keys, etc.">
                        <small>💡 Give your note a descriptive title</small>
                    </div>
                    
                    <div class="input-group">
                        <label for="noteContent">Note Content:</label>
                        <textarea id="noteContent" rows="12" placeholder="Enter your sensitive note here...

Examples:
- Database connection strings
- API keys and tokens  
- Passwords or credentials
- Configuration files
- Any sensitive text

This note will be automatically deleted after 24 hours." required></textarea>
                        <small>⏰ This note will auto-delete after <strong>24 hours</strong> for security</small>
                    </div>
                    
                    <button type="submit" id="createNoteBtn" class="primary-btn">🔗 Generate Secure Link</button>
                </form>
                
                <!-- Generated Link Display -->
                <div id="generatedLinkSection" class="input-group hidden">
                    <label>Generated Secure Link:</label>
                    <div class="textarea-with-button">
                        <input type="text" id="generatedLink" readonly placeholder="Your secure link will appear here..." />
                        <button id="copyLinkBtn" class="copy-btn">📋 Copy</button>
                    </div>
                    <div style="background: #f0f9ff; padding: 1rem; border-radius: 8px; margin-top: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span><strong>🕐 Expires:</strong> <span id="expiryTime"></span></span>
                        </div>
                        <div style="color: #dc2626; font-size: 0.875rem;">
                            <strong>🔒 Important:</strong> This link can only be viewed once and will be automatically deleted after viewing.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Page -->
        <div id="adminPage" class="page">
            <div class="page-header">
                <h2>Cluster Management</h2>
                <p class="page-description">Configure and manage your Kubernetes clusters for sealed secret encryption</p>
            </div>
            
            <div class="settings-compact-layout">
                <!-- Add Cluster Section -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3>➕ Add New Cluster</h3>
                        <p>Connect a new Kubernetes cluster with its sealed secret certificate</p>
                    </div>
                    
                    <div class="card-content">
                        <form id="addClusterForm" enctype="multipart/form-data" class="compact-form" style="flex: 1;">
                            <div class="form-group">
                                <label for="clusterName">Cluster Name *</label>
                                <input type="text" id="clusterName" placeholder="e.g., production-cluster" required>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-field full-width">
                                    <label class="field-label">
                                        <span class="label-text">Certificate Method</span>
                                    </label>
                                    <div class="method-selector">
                                        <button type="button" id="textMethodBtn" class="method-option active" data-method="text">
                                            <div class="method-icon">�</div>
                                            <div class="method-content">
                                                <div class="method-title">Paste Content</div>
                                                <div class="method-desc">Paste certificate content directly</div>
                                            </div>
                                        </button>
                                        <button type="button" id="fileMethodBtn" class="method-option" data-method="file">
                                            <div class="method-icon">�</div>
                                            <div class="method-content">
                                                <div class="method-title">Upload File</div>
                                                <div class="method-desc">Upload a .pem certificate file</div>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Text Input Method -->
                            <div id="textMethod" class="cert-input-method">
                                <div class="form-row">
                                    <div class="form-field full-width">
                                        <label for="certContent" class="field-label">
                                            <span class="label-text">Certificate Content</span>
                                            <span class="label-required">*</span>
                                        </label>
                                        <textarea id="certContent" class="field-textarea" rows="10" placeholder="-----BEGIN CERTIFICATE-----
MIIErTCCApWgAwIBAgIQKQ7F...
-----END CERTIFICATE-----"></textarea>
                                        <div class="field-hint">Paste the complete PEM certificate content including BEGIN/END lines</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- File Upload Method -->
                            <div id="fileMethod" class="cert-input-method hidden">
                                <div class="form-row">
                                    <div class="form-field full-width">
                                        <label for="certFile" class="field-label">
                                            <span class="label-text">Certificate File</span>
                                            <span class="label-required">*</span>
                                        </label>
                                        <div class="file-upload-area">
                                            <input type="file" id="certFile" accept=".pem,.crt,.cer" class="file-input">
                                            <div class="file-upload-content">
                                                <div class="file-upload-icon">📄</div>
                                                <div class="file-upload-text">
                                                    <div class="file-upload-primary">Choose certificate file</div>
                                                    <div class="file-upload-secondary">or drag and drop here</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="field-hint">Supported formats: .pem, .crt, .cer (max 5MB)</div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" id="addClusterBtn" class="btn btn-primary">
                                    <span class="btn-icon">➕</span>
                                    Add Cluster
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Clusters List Section -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3>🏢 Configured Clusters</h3>
                        <p>Manage your existing cluster configurations</p>
                    </div>
                    
                    <div class="card-content">
                        <div id="clustersList" class="clusters-compact-list"></div>
                    </div>
                </div>

                <!-- Data Management Section -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3>🗂️ Data Management</h3>
                        <p>Manage your locally stored data and preferences</p>
                    </div>
                    
                    <div class="card-content">
                        <div class="data-management-actions">
                            <button id="clearLocalDataBtn" class="btn btn-danger">
                                <span class="btn-icon">🗑️</span>
                                Clear All Saved Data
                            </button>
                            <p class="data-management-hint">This will clear all saved form data, including secret inputs, notes, and preferences stored in your browser.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="script.js"></script>
</body>
</html>